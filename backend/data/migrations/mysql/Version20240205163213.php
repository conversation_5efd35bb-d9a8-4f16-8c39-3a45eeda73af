<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240205163213 extends AbstractMigration {
    
    protected const ROBONOTE_LOGO_LINK = 'https://robonote-static-files.s3.eu-west-3.amazonaws.com/robonote-logo.png';
    protected const ATOMIX_LOGO_LINK = 'https://robonote-static-files.s3.eu-west-3.amazonaws.com/atomix-logo.png';
    protected const ROBONOTE_SENDING_EMAIL = '<EMAIL>';
    protected const ROBONOTE_SENDING_EMAIL_SENDER = 'Robonote';
    protected const ATOMIX_SENDING_EMAIL = '<EMAIL>';
    protected const ATOMIX_SENDING_EMAIL_SENDER = 'Atomix';
    protected const ATOMIX_DOMAIN = 'sai.atomix.chat';

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                fronts
            DROP COLUMN
                send_emails;
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                fronts
            ADD COLUMN
                email_logo_link VARCHAR(2047) NOT NULL
            AFTER
                logo;
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                fronts
            ADD COLUMN
                sending_email VARCHAR(255) DEFAULT NULL
            AFTER
                favicon;
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                fronts
            ADD COLUMN
                sending_email_sender VARCHAR(255) DEFAULT NULL
            AFTER
                sending_email;
        ');
        $this->connection->executeQuery('
            UPDATE
                fronts
            SET
                email_logo_link = "' . static::ROBONOTE_LOGO_LINK . '",
                sending_email = "' . static::ROBONOTE_SENDING_EMAIL . '",
                sending_email_sender = "' . static::ROBONOTE_SENDING_EMAIL_SENDER . '"
            ;
        ');
        $this->connection->executeQuery('
            UPDATE
                fronts
            SET
                email_logo_link = "' . static::ATOMIX_LOGO_LINK . '",
                sending_email = "' . static::ATOMIX_SENDING_EMAIL . '",
                sending_email_sender = "' . static::ATOMIX_SENDING_EMAIL_SENDER . '"
            WHERE
                domain = "' . static::ATOMIX_LOGO_LINK . '";
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('fronts');
        $table->dropColumn('email_logo_link');
        $table->dropColumn('sending_email');
        $table->dropColumn('sending_email_sender');
        $table->addColumn('send_emails', 'boolean', [
            'notnull' => true,
            'default' => false,
        ]);
    }

}
