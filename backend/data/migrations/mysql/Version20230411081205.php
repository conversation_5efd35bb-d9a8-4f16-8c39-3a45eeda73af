<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230411081205 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                algo_apis
            SET
                is_default_api = 1
            WHERE
                path IN (\'https://algo2.robonote.io/api/v5\', \'https://algo2.robonote.io/api/v6\', \'https://algo2.robonote.io/api/v8\')
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                algo_apis
            SET
                is_default_api = 0
            WHERE
               1
;        ');
    }

}
