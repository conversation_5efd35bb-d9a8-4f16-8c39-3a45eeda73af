<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230716134436 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis
                (path, algo_api_category_id)
            VALUES
                ("https://g4dn.robonote.io/api/v5", 1),
                ("https://g4dn.robonote.io/api/v6", 3),
                ("https://g4dn.robonote.io/api/v9", 4),
                ("https://g4dn.robonote.io/api/v11", 1),
                ("https://g4dn.robonote.io/api/v12", 3),
                ("https://g4dn.robonote.io/api/v14", 3)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery("
            DELETE
            FROM
                algo_apis
            WHERE
                path IN
                (
                    'https://g4dn.robonote.io/api/v5',
                    'https://g4dn.robonote.io/api/v6',
                    'https://g4dn.robonote.io/api/v9',
                    'https://g4dn.robonote.io/api/v11',
                    'https://g4dn.robonote.io/api/v12',
                    'https://g4dn.robonote.io/api/v14'
                )
        ");
    }

}
