<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\BigIntType;
use Doctrine\DBAL\Types\StringType;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250320063115 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('checklists_points');
        $table->modifyColumn('description', [
            'type' => new StringType(),
            'notnull' => false,
            'length' => 1024,
        ]);

    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('checklists_points');
        $table->modifyColumn('description', [
            'type' => new StringType(),
            'notnull' => true,
            'length' => 1024,
        ]);
    }
}
