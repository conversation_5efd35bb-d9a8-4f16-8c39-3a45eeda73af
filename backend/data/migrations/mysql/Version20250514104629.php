<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250514104629 extends AbstractMigration
{

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery(
            '
            UPDATE
                companies
            SET
                llm_events_limit = 20
            WHERE
                deleted = 0
        '
        );
    }

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery(
            '
                UPDATE
                    companies
                SET
                    llm_events_limit = 0
                WHERE
                    deleted = 0
            '
        );
    }

}
