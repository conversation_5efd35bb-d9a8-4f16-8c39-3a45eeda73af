<?php

namespace Clickhouse\Migrations;

class Version20240627113138 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls_events
            ADD COLUMN
                paragraph_speaker_role Nullable(Enum(\'agent\', \'client\', \'unclear\'))
            AFTER
                paragraph_start_time
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls_events
            DROP COLUMN
                paragraph_speaker_role
        ');
    }
}
