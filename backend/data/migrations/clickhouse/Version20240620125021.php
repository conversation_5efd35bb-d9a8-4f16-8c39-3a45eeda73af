<?php

namespace Clickhouse\Migrations;

class Version20240620125021 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE
                ems_data_set_examples
        ');
        $this->getClient()->write(
            '
                CREATE TABLE ems_data_set_examples
                (
                    `data_set_example_id` String,                
                    `data_set_id` String,
                    `text` String,
                    `highlight` Nullable(String) DEFAULT NULL,
                    `language` Nullable(String) DEFAULT NULL,
                    `call_id` Nullable(String) DEFAULT NULL,
                    `paragraph_number` Nullable(UInt32) DEFAULT NULL,
                    `paragraph_start_time` Nullable(UInt32) DEFAULT NULL,
                    `status` String,
                    `is_deleted` Bool DEFAULT 0,
                    `created_at` DateTime DEFAULT now(),
                    `updated_at` DateTime DEFAULT now()
                )
                ENGINE = ReplacingMergeTree
                ORDER BY (
                     data_set_example_id,
                     data_set_id
                );
            '
        );
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE
                ems_data_set_examples
        ');
        $this->getClient()->write(
            '
                CREATE TABLE ems_data_set_examples
                (
                    `ems_data_set_example_id` String,                
                    `ems_data_set_id` String,
                    `text` String,
                    `highlight` Nullable(String) DEFAULT NULL,
                    `language` String,
                    `call_id` Nullable(String) DEFAULT NULL,
                    `paragraph` Nullable(UInt32) DEFAULT NULL,
                    `paragraph_start_time` Nullable(UInt32) DEFAULT NULL,
                    `status` String,
                    `is_deleted` Bool DEFAULT 0,
                    `created_at` DateTime DEFAULT now(),
                    `updated_at` DateTime DEFAULT now()
                )
                ENGINE = ReplacingMergeTree
                ORDER BY (
                     ems_data_set_example_id,
                     ems_data_set_id
                );
            '
        );
    }
}
