<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Validator\Checklist;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Data\ChecklistsPointsTable;
use STCompany\Data\ChecklistsTable;
use STCompany\Entity\Checklist\ChecklistPointCollection;
use STCompany\Validator\Checklist\SaveChecklistPointValidator;
use tests\TestCase;

final class SaveChecklistPointValidatorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testValidationFailsWhenChecklistPointsCountEqualsMaximum(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);
        $companyId = $this->faker->numberBetween(1, 100);
        $title = $this->faker->sentence(3);
        $expectedActions = $this->faker->text(100);
        $goodPerformanceDescription = $this->faker->text(100);
        $badPerformanceDescription = $this->faker->text(100);

        $input = [
            'checklist_id' => $checklistId,
            'company_id' => $companyId,
            'title' => $title,
            'expected_actions' => $expectedActions,
            'good_performance_description' => $goodPerformanceDescription,
            'bad_performance_description' => $badPerformanceDescription,
        ];

        $checklistPointCollection = $this->createMock(ChecklistPointCollection::class);
        $checklistPointCollection->method('count')->willReturn(10);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $checklistsPointsTable
            ->method('getChecklistPointsByChecklistId')
            ->with($checklistId, $companyId)
            ->willReturn($checklistPointCollection);
        $checklistsPointsTable
            ->method('getChecklistPointByTitleAndChecklistId')
            ->with($title, $checklistId)
            ->willReturn(null);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->method('getChecklist')
            ->with($checklistId, $companyId)
            ->willReturn($this->createMock(\STCompany\Entity\Checklist\Checklist::class));

        $validator = new SaveChecklistPointValidator($checklistsPointsTable, $checklistsTable);
        $validator->setInstance($input);
        $validator->run();

        $this->assertTrue($validator->hasError('checklist_id'));
        $errors = $validator->getErrors('checklist_id');
        $this->assertContains('You can\'t create more than 10 checklist points', $errors);
    }
}
