<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoEvents\RequestCreation;

use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequest;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STIndustry\Data\IndustriesTable;
use tests\TestCase;

class LlmEventsAlgoApiRequestTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetEnv(): void
    {
        $requestParams = $this->createMock(RequestParams::class);

        $request = new LlmEventsAlgoApiRequest($this->faker->url(), $requestParams);

        $this->assertNull($request->getEnv());
    }

    public function testGetUrl(): void
    {
        $apiUrl = $this->faker->url();
        $requestParams = $this->createMock(RequestParams::class);

        $request = new LlmEventsAlgoApiRequest($apiUrl, $requestParams);

        $expectedUrl = $apiUrl . '/api/algo/promptV4';

        $this->assertSame($expectedUrl, $request->getUrl());
    }

    /**
     * @throws Exception
     */
    public function testGetParams(): void
    {
        $apiUrl = $this->faker->url();

        $requestParamsData = ['some key' => 'some value'];
        $requestParams = $this->createMock(RequestParams::class);
        $requestParams
            ->method('toArray')
            ->willReturn($requestParamsData);

        $request = new LlmEventsAlgoApiRequest($apiUrl, $requestParams);

        $this->assertSame($requestParamsData, $request->getParams());
    }

    /**
     * @throws Exception
     */
    public function testGetAlgoApiId(): void
    {
        $requestParams = $this->createMock(RequestParams::class);

        $request = new LlmEventsAlgoApiRequest($this->faker->url(), $requestParams);

        $this->assertSame(0, $request->getAlgoApiId());
    }

    /**
     * @throws Exception
     */
    public function testGetIndustryId(): void
    {
        $requestParams = $this->createMock(RequestParams::class);

        $request = new LlmEventsAlgoApiRequest($this->faker->url(), $requestParams);

        $this->assertSame(IndustriesTable::LLM, $request->getIndustryId());
    }
}
