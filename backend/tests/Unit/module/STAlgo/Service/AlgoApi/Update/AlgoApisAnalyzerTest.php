<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoApi\Update;

use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use STAlgo\Service\AlgoApi\Update\AlgoApisAnalyzer;
use STAlgo\Service\AlgoApiService;
use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesTable;
use tests\TestCase;

final class AlgoApisAnalyzerTest extends TestCase
{
    /**
     * @dataProvider algoApiParamsProvider
     * @param string $oldName
     * @param string $newName
     * @param string $oldDescription
     * @param string $newDescription
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testAnalyze(string $oldName, string $newName, string $oldDescription, string $newDescription): void
    {
        $deletedPath = $this->faker->text(10);
        $samePath = $this->faker->text(20);
        $updatedPath = $this->faker->text(30);
        $newPath = $this->faker->text(40);

        $providedSameAlgoApi = new AlgoApi();
        $providedSameAlgoApi->setPath($samePath);

        $providedUpdatedAlgoApi = new AlgoApi();
        $providedUpdatedAlgoApi->setPath($updatedPath);
        $providedUpdatedAlgoApi->setName($newName);
        $providedUpdatedAlgoApi->setDescription($newDescription);

        $providedNewAlgoApi = new AlgoApi();
        $providedNewAlgoApi->setPath($newPath);
        $providedNewAlgoApi->setName($this->faker->word());
        $providedNewAlgoApi->setDescription($this->faker->text(100));

        $providedAlgoApiCollection = new AlgoApiCollection();

        $providedAlgoApiCollection->add($providedSameAlgoApi, $samePath);
        $providedAlgoApiCollection->add($providedUpdatedAlgoApi, $updatedPath);
        $providedAlgoApiCollection->add($providedNewAlgoApi, $newPath);

        $existentDeletedAlgoApi = new AlgoApi();
        $existentDeletedAlgoApi->setPath($deletedPath);

        $existentSameAlgoApi = new AlgoApi();
        $existentSameAlgoApi->setPath($samePath);

        $existentUpdatedAlgoApi = new AlgoApi();
        $existentUpdatedAlgoApi->setPath($updatedPath);
        $existentUpdatedAlgoApi->setName($oldName);
        $existentUpdatedAlgoApi->setDescription($oldDescription);

        $existentAlgoApiCollection = new AlgoApiCollection();
        $existentAlgoApiCollection->add($existentDeletedAlgoApi, $deletedPath);
        $existentAlgoApiCollection->add($existentSameAlgoApi, $samePath);
        $existentAlgoApiCollection->add($existentUpdatedAlgoApi, $updatedPath);

        $algoApiService = $this->createMock(AlgoApiService::class);
        $algoApiService
            ->method('getNerAlgoApisIndexedByPath')
            ->willReturn($existentAlgoApiCollection);

        $analyzer = new AlgoApisAnalyzer($algoApiService);
        $algoApiCollectionToSave = $analyzer->analyze($providedAlgoApiCollection);

        $this->assertInstanceOf(AlgoApiCollection::class, $algoApiCollectionToSave);
        $this->assertFalse($algoApiCollectionToSave->isEmpty());
        $this->assertSame(3, $algoApiCollectionToSave->count());

        // Check not updated
        $this->assertFalse($algoApiCollectionToSave->keyExists($samePath));

        // Check new one
        $this->assertTrue($algoApiCollectionToSave->keyExists($newPath));

        $newAlgoApi = $algoApiCollectionToSave->offsetGet($newPath);

        $this->assertNotNull($newAlgoApi);
        $this->assertSame($newPath, $newAlgoApi->getPath());
        $this->assertSame(IndustriesTable::COMMON_INDUSTRY, $newAlgoApi->getIndustryId());
        $this->assertSame('/textV2', $newAlgoApi->getAnalyzeMethod());
        $this->assertSame($providedNewAlgoApi->getName(), $newAlgoApi->getName());
        $this->assertSame($providedNewAlgoApi->getDescription(), $newAlgoApi->getDescription());
        $this->assertFalse($newAlgoApi->getIsDeleted());
        $this->assertFalse($newAlgoApi->isDefaultApi());

        // Check updated
        $this->assertTrue($algoApiCollectionToSave->keyExists($updatedPath));

        $updatedAlgoApi = $algoApiCollectionToSave->offsetGet($updatedPath);

        $this->assertNotNull($updatedAlgoApi);
        $this->assertSame($newName, $updatedAlgoApi->getName());
        $this->assertSame($newDescription, $updatedAlgoApi->getDescription());

        // Check deleted
        $this->assertTrue($algoApiCollectionToSave->keyExists($deletedPath));
        $this->assertSame($existentDeletedAlgoApi, $algoApiCollectionToSave->offsetGet($deletedPath));

        $deletedAlgoApi = $algoApiCollectionToSave->offsetGet($deletedPath);

        $this->assertNotNull($deletedAlgoApi);
        $this->assertTrue($deletedAlgoApi->getIsDeleted());
    }

    public static function algoApiParamsProvider(): array
    {
        return [
            // Different name
            [
                'oldName' => 'old name',
                'newName' => 'new name',
                'oldDescription' => 'same description',
                'newDescription' => 'same description',
            ],
            // Different description
            [
                'oldName' => 'same name',
                'newName' => 'same name',
                'oldDescription' => 'old description',
                'newDescription' => 'new description',
            ],
            // Different name and description
            [
                'oldName' => 'old name',
                'newName' => 'new name',
                'oldDescription' => 'old description',
                'newDescription' => 'new description',
            ],
        ];
    }
}
