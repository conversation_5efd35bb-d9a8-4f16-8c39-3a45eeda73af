<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoApi;

use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Entity\AlgoApi;
use STAlgo\Service\AlgoApi\CompanyAlgoApisSelectorService;
use STLib\Mvc\Hydrator\Hydrator;
use tests\TestCase;

final class CompanyAlgoApisSelectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     * @throws ReflectionException
     */
    public function testGetAlgoApis(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $algoApi1 = $this->createMock(AlgoApi::class);
        $algoApi2 = $this->createMock(AlgoApi::class);

        $algoApiData1 = ['key1' => 'value1'];
        $algoApiData2 = ['key2' => 'value2'];

        $resultSet = new ResultSet();
        $resultSet->initialize([$algoApiData1, $algoApiData2]);

        $algoApisTable = $this->createMock(AlgoApisTable::class);
        $algoApisTable
            ->method('getCompanyNerAlgoApis')
            ->with($companyId)
            ->willReturn($resultSet);

        $hydratorMap = [
            [$algoApiData1, AlgoApi::class, true, $algoApi1],
            [$algoApiData2, AlgoApi::class, true, $algoApi2]
        ];
        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->willReturnMap($hydratorMap);

        $selector = new CompanyAlgoApisSelectorService($algoApisTable, $hydrator);
        $algoApiCollection = $selector->getNerAlgoApis($companyId);

        $this->assertFalse($algoApiCollection->isEmpty());
        $this->assertSame(2, $algoApiCollection->count());

        $this->assertSame($algoApi1, $algoApiCollection[0]);
        $this->assertSame($algoApi2, $algoApiCollection[1]);
    }
}
