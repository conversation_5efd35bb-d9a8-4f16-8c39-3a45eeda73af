<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service;

use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Service\AiSolutionsCommutatorService;
use STAlgo\Service\AlgoEvents\RequestCreation\EventsAlgoApiRequestInterface;
use STAlgo\Service\AlgoEvents\RequestCreation\ResponseToAlgoEventConverter;
use STAlgo\Service\Client;
use STAlgo\Service\Interfaces\TranslatorInterface;
use STCall\Entity\AlgoEventCollection;
use STCall\Entity\Call;
use STCall\Entity\Paragraph;
use STCall\Entity\ParagraphCollection;
use STCompany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Checklist\ChecklistPointCollection;
use STCompany\Entity\Company;
use tests\TestCase;
use tests\WithConsecutive;

use function PHPUnit\Framework\assertNotEmpty;
use function PHPUnit\Framework\assertTrue;

class AiSolutionsCommutatorServiceTest extends TestCase
{
    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testGetAlgoEventsFromCall(): void
    {
        $company = $this->createMock(Company::class);
        $call = $this->createMock(Call::class);

        $translator = $this->createMock(TranslatorInterface::class);

        $request1 = $this->createMock(EventsAlgoApiRequestInterface::class);
        $request2 = $this->createMock(EventsAlgoApiRequestInterface::class);

        $result1 = ['some result key 1' => 'some result value 1'];
        $result2 = ['some result key 1' => 'some result value 1'];

        $jsonResult1 = json_encode($result1);
        $jsonResult2 = json_encode($result2);
        $clientAnalyzeMap = [
            [$request1, $jsonResult1],
            [$request2, $jsonResult2],
        ];
        $client = $this->createMock(Client::class);
        $client
            ->method('analyze')
            ->willReturnMap($clientAnalyzeMap);

        $algoEventCollection = new AlgoEventCollection();
        $responseToAlgoEventConverter = $this->createMock(ResponseToAlgoEventConverter::class);
        $responseToAlgoEventConverter
            ->expects($this->exactly(2))
            ->method('convert')
            ->with(
                ...
                WithConsecutive::create(
                    [$algoEventCollection, $jsonResult1, $company, $call, $request1],
                    [$algoEventCollection, $jsonResult2, $company, $call, $request2],
                )
            );

        $commutator = new AiSolutionsCommutatorService($client, $translator, $responseToAlgoEventConverter);
        $commutator->getAlgoEventsFromCall($company, $call, [$request1, $request2]);
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testChecklistResult(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $callId = $this->faker->text();
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getCompanyId')->willReturn($companyId);

        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph1->method('getParagraphNumber')->willReturn(0);
        $paragraph1->method('getText')->willReturn($this->faker->text(100));
        $paragraph1->method('getSpeakerNumber')->willReturn(0);
        $paragraph2 = $this->createMock(Paragraph::class);
        $paragraph2->method('getParagraphNumber')->willReturn(0);
        $paragraph2->method('getText')->willReturn($this->faker->text(100));
        $paragraph2->method('getSpeakerNumber')->willReturn(0);
        $paragraphsCollection = new ParagraphCollection([
            $paragraph1->getParagraphNumber() => $paragraph1,
            $paragraph2->getParagraphNumber() => $paragraph2,
        ]);

        $checklistPoint = $this->createMock(ChecklistPoint::class);
        $checklistPoint->method('getTitle')->willReturn('Re-engaging the Lead');
        $checklistPoint->method('getDescription')->willReturn(
            'Explain the reason for contacting the lead '
        );
        $checklistPoint->method('getExpectedActions')->willReturn(
            'Explain the reason for the call and ask questions to understand the lead’s interest'
        );
        $checklistPoint->method('getGoodPerformanceDescription')->willReturn(
            'Clearly explains the reason for contact and asks open-ended questions about the material'
        );
        $checklistPoint->method('getGoodPerformanceExample')->willReturn(
            'I call to tell about our new sales'
        );
        $checklistPoint->method('getBadPerformanceDescription')->willReturn(
            'Does not clarify the reason for the call or fails to explore the lead’s interest'
        );
        $checklistPoint->method('getBadPerformanceExample')->willReturn(
            'Hello, I want to speak with you'
        );
        $checklistPointsCollections = new ChecklistPointCollection([
            $checklistPoint->getId() => $checklistPoint,
        ]);

        $translator = $this->createMock(TranslatorInterface::class);
        $client = $this->createMock(Client::class);
        $client
            ->method('getChecklistResult')
            ->willReturn([
                'status' => 'ok',
                'results' =>  [
                    'checklist_result' => [
                        [
                            'title' => 'Re-engaging the Lead',
                            'decision' => 'Not a Positive Event',
                            'cot' => 'The call transcript does not provide any context about the reason for the call',
                        ],
                    ],
                    'company_id' => $companyId,
                    'call_id' => $call->getId()
                ]
            ]);

        $commutator = new AiSolutionsCommutatorService(
            $client,
            $translator,
            $this->createMock(ResponseToAlgoEventConverter::class)
        );
        $checklistResult = $commutator->getChecklistResult($call, $paragraphsCollection, $checklistPointsCollections);

        assertTrue($checklistResult->status === 'ok');
        assertNotEmpty($checklistResult->getChecklistResult());
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     * @throws \JsonException
     */
    public function testGetParagraphsSpeakersRolesEnTextFallback(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text();
        
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getCompanyId')->willReturn($companyId);
        $call->method('getLanguage')->willReturn(null);
        
        // Create paragraph with both text and enText
        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph1->method('getParagraphNumber')->willReturn(1);
        $originalText1 = $this->faker->text(100);
        $englishText1 = $this->faker->text(100);
        $paragraph1->method('getText')->willReturn($originalText1);
        $paragraph1->method('getEnText')->willReturn($englishText1);
        $paragraph1->method('getSpeakerNumber')->willReturn(1);
        
        // Create paragraph with only text (enText is null)
        $paragraph2 = $this->createMock(Paragraph::class);
        $paragraph2->method('getParagraphNumber')->willReturn(2);
        $originalText2 = $this->faker->text(100);
        $paragraph2->method('getText')->willReturn($originalText2);
        $paragraph2->method('getEnText')->willReturn(null);
        $paragraph2->method('getSpeakerNumber')->willReturn(2);
        
        $paragraphsCollection = new ParagraphCollection([
            $paragraph1->getParagraphNumber() => $paragraph1,
            $paragraph2->getParagraphNumber() => $paragraph2,
        ]);
        
        // Expected request parameters
        $expectedRequestParams = [
            'paragraphs' => [
                [
                    'id' => 1,
                    'text' => $originalText1,
                    'en_text' => $englishText1,
                    'speaker_number' => 1,
                ],
                [
                    'id' => 2,
                    'text' => $originalText2,
                    'en_text' => $originalText2,
                    'speaker_number' => 2,
                ],
            ],
            'company_id' => (string) $companyId,
            'call_id' => $callId,
        ];

        $speakersResult = [
            'status' => 'ok',
            'results' => [
                'speakers' => [
                    ['speaker_number' => 1, 'role' => 'agent'],
                    ['speaker_number' => 2, 'role' => 'client'],
                ]
            ]
        ];
        
        $translator = $this->createMock(TranslatorInterface::class);
        $client = $this->createMock(Client::class);
        $client
            ->expects($this->once())
            ->method('getSpeakers')
            ->with($expectedRequestParams)
            ->willReturn(json_encode($speakersResult, JSON_THROW_ON_ERROR));
        
        $commutator = new AiSolutionsCommutatorService(
            $client,
            $translator,
            $this->createMock(ResponseToAlgoEventConverter::class)
        );
        
        $result = $commutator->getParagraphsSpeakersRoles($call, $paragraphsCollection);
        
        $this->assertEquals('ok', $result->status);
        $this->assertEquals($speakersResult['results'], $result->results);
    }
}
