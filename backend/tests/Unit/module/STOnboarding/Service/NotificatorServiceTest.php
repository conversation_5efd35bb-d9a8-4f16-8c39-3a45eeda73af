<?php

declare(strict_types=1);

namespace tests\Unit\module\STOnboarding\Service;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Service\Interfaces\ConfigurationInterface;
use STOnboarding\Service\Notification\Interfaces\SenderInterface;
use STOnboarding\Service\Notification\OnboardingFormChecker;
use STOnboarding\Service\NotificatorService;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

final class NotificatorServiceTest extends TestCase
{
    /**
     * @throws PHPUnitException
     */
    public function testNotifyWhenCompleted(): void
    {
        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $companyName = $this->faker->word();
        $channel = $this->faker->text(15);

        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getExternalId')->willReturn($externalId);
        $onboardingForm->method('getFrontFormLink')->willReturn($frontFormLink);
        $onboardingForm->method('getCompanyName')->willReturn($companyName);

        $unfilledFields = [
            $this->faker->randomElement([
                'company_logo',
                'calls_settings'
            ]),
            $this->faker->randomElement([
                'company_logo',
                'calls_settings'
            ]),
        ];

        $header = '*Completed* onboarding form has been sent.';
        $params = [
            'Company name' => $companyName,
            'Form ID' => $externalId,
            'Link' => $frontFormLink,
            'Unfilled fields' => $unfilledFields,
        ];

        $onboardingFormChecker = $this->createMock(OnboardingFormChecker::class);
        $onboardingFormChecker
            ->method('getUnfilledFields')
            ->willReturn($unfilledFields);
        $onboardingFormChecker
            ->method('isFormCompleted')
            ->willReturn(true);

        $slackConfig = [
            'channels' => [
                'onboarding' => $channel,
            ]
        ];

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('slack')
            ->willReturn($slackConfig);

        $sender = $this->createMock(SenderInterface::class);
        $sender
            ->expects($this->once())
            ->method('send')
            ->with($header, $params, $channel);

        $dataCollector = $this->createMock(DataCollector::class);

        $notifier = new NotificatorService($onboardingFormChecker, $configuration, $sender, $dataCollector);
        $notifier->notifyAboutFormSubmission($onboardingForm);
    }

    /**
     * @throws PHPUnitException
     */
    public function testNotifyWhenNotCompleted(): void
    {
        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $companyName = $this->faker->word();
        $channel = $this->faker->text(15);

        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getExternalId')->willReturn($externalId);
        $onboardingForm->method('getFrontFormLink')->willReturn($frontFormLink);
        $onboardingForm->method('getCompanyName')->willReturn($companyName);

        $unfilledFields = [
            $this->faker->randomElement([
                'company_name',
                'company_logo',
                'users',
                'industries',
                'calls_settings'
            ]),
            $this->faker->randomElement([
                'company_name',
                'company_logo',
                'users',
                'industries',
                'calls_settings'
            ]),
        ];
        $onboardingFormChecker = $this->createMock(OnboardingFormChecker::class);
        $onboardingFormChecker
            ->method('getUnfilledFields')
            ->willReturn($unfilledFields);
        $onboardingFormChecker
            ->method('isFormCompleted')
            ->willReturn(false);

        $header = '*Incomplete* onboarding form has been sent.';
        $params = [
            'Company name' => $companyName,
            'Form ID' => $externalId,
            'Link' => $frontFormLink,
            'Unfilled fields' => $unfilledFields,
        ];

        $slackConfig = [
            'channels' => [
                'onboarding' => $channel,
            ]
        ];

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('slack')
            ->willReturn($slackConfig);

        $sender = $this->createMock(SenderInterface::class);
        $sender
            ->expects($this->once())
            ->method('send')
            ->with($header, $params, $channel);

        $dataCollector = $this->createMock(DataCollector::class);

        $notifier = new NotificatorService($onboardingFormChecker, $configuration, $sender, $dataCollector);
        $notifier->notifyAboutFormSubmission($onboardingForm);
    }

    /**
     * @throws PHPUnitException
     */
    public function testNotifyWhenSomeError(): void
    {
        $roboTruckEventName = 'onboarding_notification';

        $onboardingForm = $this->createMock(OnboardingForm::class);

        $errorMessage = $this->faker->sentence();
        $code = $this->faker->numberBetween(0, 100);

        $exception = new Exception($errorMessage, $code);

        $roboTruckEventMessage = 'Onboarding notification failed.';
        $roboTruckEventExtra = [
            'error message' => $errorMessage,
            'code' => $code,
            'stack_trace' => $exception->getTrace(),
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra);

        $onboardingFormChecker = $this->createMock(OnboardingFormChecker::class);
        $onboardingFormChecker
            ->method('getUnfilledFields')
            ->willThrowException($exception);
        $configuration = $this->createMock(ConfigurationInterface::class);

        $sender = $this->createMock(SenderInterface::class);

        $notifier = new NotificatorService($onboardingFormChecker, $configuration, $sender, $dataCollector);
        $notifier->notifyAboutFormSubmission($onboardingForm);
    }
}
