<?php

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use PHPUnit\Framework\MockObject\Exception;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use STCall\Data\TranscribeDriversLanguagesTable;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\TranscribingDistributionStep;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

class TranscribingDistributionStepTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testRun()
    {
        $roboTruckEventName = 'call_analyze_transcribing_distribution_step_success';

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text();

        $call = $this->createMock(Call::class);
        $call
            ->method('getId')
            ->willReturn($callId);

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callFactory
            ->method('createCall')
            ->willReturn($call);

        $transcribeDriversLanguagesTable = $this->createMock(TranscribeDriversLanguagesTable::class);

        $roboTruckEventMessage = 'Driver is deepgramWhisper';
        $roboTruckEventExtra = [
            'id' => null,
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, $oldLogName);

        $step = new TranscribingDistributionStep($callsTable, $callFactory, $transcribeDriversLanguagesTable, $dataCollector);
        $step->launch($callId, $companyId);

        $this->assertArrayHasKey('driver', $step->getNextStepOptions());
        $this->assertSame('deepgramWhisper', $step->getNextStepOptions()['driver']);
        $this->assertSame('call-transcribing-whisper-step', $step->getNextStepQueue());
    }

    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testRunWhenDeepgramNova3Driver()
    {
        $roboTruckEventName = 'call_analyze_transcribing_distribution_step_success';

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text();

        $language = $this->faker->languageCode();
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getLanguage')->willReturn($language);

        $driverName = 'deepgramNova3';

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callFactory
            ->method('createCall')
            ->willReturn($call);

        $transcribeDriversLanguagesTable = $this->createMock(TranscribeDriversLanguagesTable::class);
        $transcribeDriversLanguagesTable
            ->method('getDriverNameByLanguage')
            ->with($language)
            ->willReturn($driverName);

        $roboTruckEventMessage = 'Driver is ' . $driverName;
        $roboTruckEventExtra = [
            'id' => null,
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, $oldLogName);

        $step = new TranscribingDistributionStep($callsTable, $callFactory, $transcribeDriversLanguagesTable, $dataCollector);
        $step->launch($callId, $companyId);

        $this->assertSame('call-transcribing-step', $step->getNextStepQueue());
        $this->assertSame(['driver' => $driverName], $step->getNextStepOptions());
    }
}
