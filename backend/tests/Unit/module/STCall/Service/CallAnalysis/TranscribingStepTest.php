<?php

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use Laminas\Db\ResultSet\ResultSetInterface;
use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Data\CallsParagraphsTable;
use ST<PERSON>all\Data\CallsTable;
use ST<PERSON>all\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Entity\Paragraph;
use STCall\Entity\ParagraphCollection;
use STCall\Service\CallAnalysis\TranscribingDriver\OneStepDriverInterface;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\CallAnalysis\TranscribingStep;
use STCall\Service\EventTriggerService;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STCompany\Entity\CompanyVocabularyCollection;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;
use tests\WithConsecutive;

class TranscribingStepTest extends TestCase
{
    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testLaunch(): void
    {
        $roboTruckStartEventName = 'call_analyze_transcribing_step_started';
        $roboTruckEndEventName = 'call_analyze_transcribing_step_ended';

        $companyId = $this->faker->numberBetween(1, 100);
        $paidTranscribingTime = $this->faker->numberBetween(51, 100);

        $callId = $this->faker->text();
        $callDuration = $this->faker->numberBetween(1, 50);

        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getCompanyId')->willReturn($companyId);
        $call->method('getDuration')->willReturn($callDuration);

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callFactory->method('createCall')->willReturn($call);

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getPaidTranscribingTime')->willReturn((float) $paidTranscribingTime);

        $companyData = ['some company data'];
        $resultSet = $this->createMock(ResultSetInterface::class);
        $resultSet
            ->method('current')
            ->willReturn($companyData);

        $companiesTable = $this->createMock(CompaniesTable::class);
        $companiesTable
            ->method('getCompany')
            ->with($companyId)
            ->willReturn($resultSet);
        $companiesTable
            ->expects($this->once())
            ->method('partialUpdateCompany')
            ->with($companyId, ['paid_transcribing_time' => $paidTranscribingTime - $callDuration]);

        $hydrator = $this->createMock(Hydrator::class);
        $hydrator->method('hydrateClass')->with($companyData, Company::class)->willReturn($company);

        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph2 = $this->createMock(Paragraph::class);

        $paragraphData1 = [$this->faker->word() => $this->faker->word(), $this->faker->word() => $this->faker->word()];
        $paragraphData2 = [
            $this->faker->word() => $this->faker->sentence(),
            $this->faker->word() => $this->faker->word()
        ];

        $paragraph1->method('toArray')->willReturn($paragraphData1);
        $paragraph2->method('toArray')->willReturn($paragraphData2);

        $paragraphCollection = new ParagraphCollection([$paragraph1, $paragraph2]);

        $driver = $this->createMock(OneStepDriverInterface::class);
        $driver->method('transcribe')->willReturn($paragraphCollection);

        $driverProvider = $this->createMock(DriverProvider::class);
        $driverProvider->method('provide')->willReturn($driver);

        $paragraphsData = [$paragraphData1, $paragraphData2];

        $callsParagraphsTable = $this->createMock(CallsParagraphsTable::class);
        $companyVocabularyService = $this->createMock(CompanyVocabularyService::class);

        $eventName = 'call-transcribing-step-finished';
        $eventParams = [
            'queue_name' => 'call-transcribing-step',
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $paragraphsData
        ];

        $eventTriggerService = $this->createMock(EventTriggerService::class);
        $eventTriggerService
            ->expects($this->once())
            ->method('trigger')
            ->with($eventName, $eventParams);

        $roboTruckEventStartMessage = 'Transcribing is started';
        $roboTruckEventEndMessage = 'Transcribing is ended';
        $roboTruckEventExtra = [
            'id' => '',
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->exactly(2))
            ->method('collect')
            ->with(
                ...
                WithConsecutive::create(
                    [$roboTruckStartEventName, $roboTruckEventStartMessage, $roboTruckEventExtra, $oldLogName],
                    [$roboTruckEndEventName, $roboTruckEventEndMessage, $roboTruckEventExtra, $oldLogName],
                )
            );

        $step = new TranscribingStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $eventTriggerService,
            $dataCollector,
            [],
            [],
        );
        $step->applyOptions([
            'driver' => 'aws'
        ]);

        $step->launch($callId, $companyId);
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testLaunchWhenDeepgramNova3Driver(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $paidTranscribingTime = $this->faker->numberBetween(51, 100);

        $callId = $this->faker->text();
        $callDuration = $this->faker->numberBetween(1, 50);

        $driverName = 'deepgramNova3';

        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getCompanyId')->willReturn($companyId);
        $call->method('getDuration')->willReturn($callDuration);

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callFactory->method('createCall')->willReturn($call);

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getPaidTranscribingTime')->willReturn((float) $paidTranscribingTime);

        $companyData = ['some company data'];
        $resultSet = $this->createMock(ResultSetInterface::class);
        $resultSet
            ->method('current')
            ->willReturn($companyData);

        $companiesTable = $this->createMock(CompaniesTable::class);
        $companiesTable
            ->method('getCompany')
            ->with($companyId)
            ->willReturn($resultSet);
        $companiesTable
            ->expects($this->once())
            ->method('partialUpdateCompany')
            ->with($companyId, ['paid_transcribing_time' => $paidTranscribingTime - $callDuration]);

        $hydrator = $this->createMock(Hydrator::class);
        $hydrator->method('hydrateClass')->with($companyData, Company::class)->willReturn($company);

        $companyVocabularyCollection = $this->createMock(CompanyVocabularyCollection::class);

        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph2 = $this->createMock(Paragraph::class);

        $paragraphData1 = [$this->faker->word() => $this->faker->word(), $this->faker->word() => $this->faker->word()];
        $paragraphData2 = [
            $this->faker->word() => $this->faker->sentence(),
            $this->faker->word() => $this->faker->word()
        ];

        $paragraph1->method('toArray')->willReturn($paragraphData1);
        $paragraph2->method('toArray')->willReturn($paragraphData2);

        $paragraphCollection = new ParagraphCollection([$paragraph1, $paragraph2]);

        $driver = $this->createMock(OneStepDriverInterface::class);
        $driver->method('transcribe')->willReturn($paragraphCollection);

        $deepgramConfig = [$this->faker->word() => $this->faker->word()];
        $awsConfig = [];

        $driverProvider = $this->createMock(DriverProvider::class);
        $driverProvider
            ->method('provide')
            ->with($driverName, $awsConfig, $deepgramConfig, $company, $call, $companyVocabularyCollection)
            ->willReturn($driver);

        $paragraphsData = [$paragraphData1, $paragraphData2];

        $callsParagraphsTable = $this->createMock(CallsParagraphsTable::class);
        $companyVocabularyService = $this->createMock(CompanyVocabularyService::class);
        $companyVocabularyService
            ->method('getCompanyVocabulary')
            ->with($companyId)
            ->willReturn($companyVocabularyCollection);

        $eventName = 'call-transcribing-step-finished';
        $eventParams = [
            'queue_name' => 'call-transcribing-step',
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $paragraphsData
        ];

        $eventTriggerService = $this->createMock(EventTriggerService::class);
        $eventTriggerService
            ->expects($this->once())
            ->method('trigger')
            ->with($eventName, $eventParams);

        $dataCollector = $this->createMock(DataCollector::class);

        $step = new TranscribingStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $eventTriggerService,
            $dataCollector,
            $awsConfig,
            $deepgramConfig
        );
        $step->applyOptions([
            'driver' => $driverName
        ]);

        $step->launch($callId, $companyId);
    }
}
