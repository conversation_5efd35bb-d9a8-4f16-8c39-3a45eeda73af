<?php

declare(strict_types=1);

namespace tests\Unit\module\STIndustry\Service\LlmEvent;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesLlmEventsTable;
use STIndustry\Entity\LlmEvent\LlmEvent as IndustryLlmEvent;
use STIndustry\Service\LlmEvent\LlmEventConnectorService;
use tests\TestCase;

final class LlmEventConnectorServiceTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testConnect(): void
    {
        $industryId = $this->faker->numberBetween(1, 100);
        $llmEventId = $this->faker->numberBetween(101, 200);

        $companyLlmEvent = $this->createMock(IndustryLlmEvent::class);
        $llmEventsTable = $this->createMock(IndustriesLlmEventsTable::class);
        $llmEventsTable
            ->expects($this->once())
            ->method('saveEvent')
            ->with(
                self::callback(
                    function (IndustryLlmEvent $industryLlmEvent) use ($industryId, $llmEventId) {
                        return $industryLlmEvent->getIndustryId() === $industryId
                            && $industryLlmEvent->getId() === $llmEventId;
                    }
                ),
            );
        $llmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId, $industryId)
            ->willReturn($companyLlmEvent);

        $connector = new LlmEventConnectorService($llmEventsTable);

        $this->assertSame($companyLlmEvent, $connector->connect($llmEventId, $industryId));
    }

    /**
     * @throws Exception
     */
    public function testDisconnect(): void
    {
        $industryId = $this->faker->numberBetween(1, 100);
        $llmEventId = $this->faker->numberBetween(101, 200);

        $llmEventsTable = $this->createMock(IndustriesLlmEventsTable::class);
        $llmEventsTable
            ->expects($this->once())
            ->method('deleteEvent')
            ->with($llmEventId, $industryId);

        $connector = new LlmEventConnectorService($llmEventsTable);
        $connector->disconnect($llmEventId, $industryId);
    }
}
