<?php

declare(strict_types=1);

namespace tests\Unit\module\STTranslation\Service;

use STTranslation\Service\TextCombiner;
use tests\TestCase;

final class TextCombinerTest extends TestCase
{
    // Please do not change the type of quotes in the value of this constant,
    // otherwise the translation will not work correctly. Quotes must be double.
    private const string TEST_DELIMITER = "\n\n\n_ ";

    /**
     * @dataProvider textsDataProvider
     * @param array $texts
     * @param array $expectedResult
     * @return void
     */
    public function testCombine(array $texts, array $expectedResult): void
    {
        $limit = 200;

        $combiner = new TextCombiner();
        $this->assertSame($expectedResult, $combiner->combine($texts, $limit));
    }

    public static function textsDataProvider(): array
    {
        return [
            // Less than limit
            [
                [
                    str_repeat('a', 80),
                    str_repeat('b', 90),
                    str_repeat('c', 19)
                ],
                [
                    str_repeat('a', 80) . self::TEST_DELIMITER . str_repeat(
                        'b',
                        90
                    ) . self::TEST_DELIMITER . str_repeat('c', 19)
                ]
            ],
            // Equals to limit
            [
                [
                    str_repeat('a', 80),
                    str_repeat('b', 90),
                    str_repeat('c', 20)
                ],
                [
                    str_repeat('a', 80) . self::TEST_DELIMITER . str_repeat(
                        'b',
                        90
                    ) . self::TEST_DELIMITER . str_repeat('c', 20)
                ]
            ],
            // Bigger than limit
            [
                [
                    str_repeat('a', 80),
                    str_repeat('b', 90),
                    str_repeat('c', 21)
                ],
                [
                    str_repeat('a', 80) . self::TEST_DELIMITER . str_repeat('b', 90),
                    str_repeat('c', 21),
                ]
            ],
            //  One paragraph bigger than limit
            [
                [
                    str_repeat('a', 80),
                    str_repeat('b', 201),
                    str_repeat('c', 21)
                ],
                [
                    str_repeat('a', 80),
                    str_repeat('b', 201),
                    str_repeat('c', 21),
                ]
            ],
            //  One paragraph bigger than limit
            [
                [
                    str_repeat('a', 80),
                    str_repeat('b', 90),
                    str_repeat('c', 201),
                    str_repeat('d', 21)
                ],
                [
                    str_repeat('a', 80) . self::TEST_DELIMITER . str_repeat('b', 90),
                    str_repeat('c', 201),
                    str_repeat('d', 21),
                ]
            ],
            //  First paragraph bigger than limit
            [
                [
                    str_repeat('a', 201),
                    str_repeat('b', 80),
                    str_repeat('c', 90),
                    str_repeat('d', 21)
                ],
                [
                    str_repeat('a', 201),
                    str_repeat('b', 80) . self::TEST_DELIMITER . str_repeat('c', 90),
                    str_repeat('d', 21),
                ]
            ],
            //  Last paragraph bigger than limit
            [
                [
                    str_repeat('a', 80),
                    str_repeat('b', 90),
                    str_repeat('c', 21),
                    str_repeat('d', 201),
                ],
                [
                    str_repeat('a', 80) . self::TEST_DELIMITER . str_repeat('b', 90),
                    str_repeat('c', 21),
                    str_repeat('d', 201),
                ]
            ]
        ];
    }

    public function testSplit(): void
    {
        $text1 = 'Translated text 1';
        $text2 = 'Translated text 2';
        $text3 = 'Translated text 3';
        $text4 = 'Translated text 4';

        $translatedTexts = [$text1, $text2, $text3, $text4];

        $combinedTranslatedTexts = [
            $text1 . self::TEST_DELIMITER . $text2 . self::TEST_DELIMITER . $text3,
            $text4
        ];

        $combiner = new TextCombiner();
        $this->assertSame($translatedTexts, $combiner->split($combinedTranslatedTexts));
    }
}
