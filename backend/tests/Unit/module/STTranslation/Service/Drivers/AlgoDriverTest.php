<?php

declare(strict_types=1);

namespace tests\Unit\module\STTranslation\Service\Drivers;

use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\MockObject\Exception;
use RuntimeException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STTranslation\Service\Drivers\AlgoDriver;
use STTranslation\Service\Drivers\Interfaces\AlgoClientInterface;
use STTranslation\Service\LanguageDetector;
use tests\TestCase;

final class AlgoDriverTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testTranslateBatch(): void
    {
        $languageCode = $this->faker->languageCode();
        $texts = [
            $this->faker->text(60),
            $this->faker->text(100),
            $this->faker->text(55),
        ];

        $requestData = [
            'language' => $languageCode,
            'paragraphs' => [
                ['id' => 0, 'text' => $texts[0]],
                ['id' => 1, 'text' => $texts[1]],
                ['id' => 2, 'text' => $texts[2]],
            ],
        ];

        $translatedTexts = [
            $this->faker->text(60),
            $this->faker->text(100),
            $this->faker->text(55),
        ];

        $responseData = [
            'status' => 'ok',
            'results' => [
                'segments' => $translatedTexts
            ]
        ];

        $algoClient = $this->createMock(AlgoClientInterface::class);
        $algoClient
            ->method('translate')
            ->with($requestData)
            ->willReturn($responseData);

        $languageDetector = $this->createMock(LanguageDetector::class);

        $driver = new AlgoDriver($algoClient, $languageDetector);
        $this->assertSame($translatedTexts, $driver->translateBatch($texts, $languageCode));
    }

    /**
     * @dataProvider wrongResponseDataProvider
     * @param array $responseData
     * @return void
     * @throws Exception
     */
    public function testTranslateBatchWhenResponseIsNotOk(array $responseData): void
    {
        $languageCode = $this->faker->languageCode();
        $requestData = [
            'language' => $languageCode,
            'paragraphs' => [],
        ];

        $algoClient = $this->createMock(AlgoClientInterface::class);
        $algoClient
            ->method('translate')
            ->with($requestData)
            ->willReturn($responseData);

        $languageDetector = $this->createMock(LanguageDetector::class);

        $driver = new AlgoDriver($algoClient, $languageDetector);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Unexpected Algo translator response: ' . json_encode($responseData));

        $driver->translateBatch([], $languageCode);
    }

    public static function wrongResponseDataProvider(): array
    {
        return [
            [[]],
            [['status' => 'not ok']],
            [['status' => 'ok']],
            [['status' => 'ok', 'results' => '']],
            [['status' => 'ok', 'results' => []]],
            [['status' => 'ok', 'results' => ['segments' => '']]],
        ];
    }

    /**
     * @return void
     * @throws Exception
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    public function testTranslate(): void
    {
        $languageCode = $this->faker->languageCode();
        $text = $this->faker->text(60);
        $translatedText = $this->faker->text(50);

        $algoClient = $this->createMock(AlgoClientInterface::class);

        $languageDetector = $this->createMock(LanguageDetector::class);
        $languageDetector->method('detectLanguageByText')->with($text)->willReturn($languageCode);

        $driver = $this->getMockBuilder(AlgoDriver::class)
            ->setConstructorArgs([$algoClient, $languageDetector])
            ->onlyMethods(['translateBatch'])
            ->getMock();
        $driver
            ->method('translateBatch')
            ->with([$text], $languageCode)
            ->willReturn([$translatedText]);

        $this->assertSame($translatedText, $driver->translate($text));
    }

    /**
     * @throws GuzzleException
     * @throws Exception
     * @throws ThirdPartyApiException
     */
    public function testTranslateWhenEmptyResponse(): void
    {
        $languageCode = $this->faker->languageCode();
        $text = $this->faker->text(60);

        $algoClient = $this->createMock(AlgoClientInterface::class);

        $languageDetector = $this->createMock(LanguageDetector::class);
        $languageDetector->method('detectLanguageByText')->with($text)->willReturn($languageCode);

        $driver = $this->getMockBuilder(AlgoDriver::class)
            ->setConstructorArgs([$algoClient, $languageDetector])
            ->onlyMethods(['translateBatch'])
            ->getMock();
        $driver
            ->method('translateBatch')
            ->with([$text], $languageCode)
            ->willReturn([]);

        $this->assertSame('', $driver->translate($text));
    }

    /**
     * @throws Exception
     */
    public function testIsSupportBatchTranslation(): void
    {
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $languageDetector = $this->createMock(LanguageDetector::class);

        $driver = new AlgoDriver($algoClient, $languageDetector);
        $this->assertTrue($driver->isSupportBatchTranslation());
    }
}
