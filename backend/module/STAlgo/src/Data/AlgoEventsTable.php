<?php

declare(strict_types=1);

namespace STAlgo\Data;

use Laminas\Db\ResultSet\ResultSet;
use Laminas\Db\ResultSet\ResultSetInterface;
use Laminas\Db\Sql\Expression;
use <PERSON>inas\Db\Sql\Select;
use STLib\Db\AbstractTable;

class AlgoEventsTable extends AbstractTable
{
    /**
     * @param int $companyId
     * @return ResultSet
     */
    public function getAlgoEvents(int $companyId): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'aeh' => 'algo_events_hints',
                ],
                new Expression(
                    'aeh.algo_event = algo_events.algo_event AND aeh.algo_api_id = algo_events.algo_api_id'
                ),
                [
                    'algo_event_hint',
                ],
                Select::JOIN_LEFT
            )
            ->join(
                [
                    'aa' => 'algo_apis',
                ],
                'aa.algo_api_id = algo_events.algo_api_id',
                [],
                Select::JOIN_INNER
            )
            ->join(
                [
                    'i' => 'industries',
                ],
                'i.industry_id = aa.industry_id',
                [
                    'industry_id' => 'industry_id',
                    'industry_name' => 'name',
                ],
                Select::JOIN_INNER
            )
            ->join(
                [
                    'caa' => 'companies_algo_apis',
                ],
                'caa.algo_api_id = aa.algo_api_id',
                [],
                Select::JOIN_INNER
            )
            ->where([
                'caa.company_id' => $companyId,
                'aa.is_deleted' => 0
            ]);
        return $this->tableGateway->selectWith($select);
    }

    public function getNerAlgoEvents(array $algoApiIds): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'aeh' => 'algo_events_hints',
                ],
                new Expression(
                    'aeh.algo_event = algo_events.algo_event AND aeh.algo_api_id = algo_events.algo_api_id'
                ),
                [
                    'algo_event_hint',
                ],
                Select::JOIN_LEFT
            )
            ->join(
                [
                    'aa' => 'algo_apis',
                ],
                'aa.algo_api_id = algo_events.algo_api_id',
                [],
                Select::JOIN_INNER
            )
            ->where([
                'aa.algo_api_id' => $algoApiIds,
                'aa.is_deleted' => 0
            ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @return ResultSet
     */
    public function getAlgoEventsNames(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'algo_event',
        ]);

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $algoApiId
     * @param array $algoEvents
     * @return int
     */
    public function insertAlgoEvents(int $algoApiId, array $algoEvents): int
    {
        if (empty($algoEvents)) {
            return 0;
        }
        $data = [];
        foreach ($algoEvents as $algoEvent) {
            $data[] = [
                'algo_api_id' => $algoApiId,
                'algo_event' => $algoEvent,
            ];
        }
        return $this->multiInsert($data, useTransaction: false);
    }

    /**
     *
     * @param int $algoApiId
     * @return void
     */
    public function deleteAlgoEvents(int $algoApiId): void
    {
        $this->tableGateway->delete([
            'algo_api_id' => $algoApiId,
        ]);
    }
}
