<?php

declare(strict_types=1);

namespace STAlgo;

use STAlgo\ServiceProviding\AlgoServiceProvider;
use STCompany\Service\Interfaces\AlgoApiSelectorInterface;
use STLib\Mvc\Data\TableFactory;
use STLib\Mvc\DependencyInjection\DefaultFactory;
use STTranslation\Service\Drivers\Interfaces\AlgoClientInterface as TranslationAlgoClientInterface;
use STCall\Service\CallAnalysis\Summarization\Interfaces\CallSummarizationGetterInterface as CallSummarizationGetterInterface;

return [
    'controller_plugins' => [
        'invokables' => [
            'algo' => Controller\Plugin\Algo::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Data\AlgoApisTable::class => TableFactory::class,
            Data\AlgoEventsTable::class => TableFactory::class,
            Data\CompaniesAlgoApisTable::class => TableFactory::class,
            Data\AlgoEventsHintsTable::class => TableFactory::class,
            Service\AlgoEventService::class => DefaultFactory::class,
            Service\AlgoApiService::class => DefaultFactory::class,
            Service\AiSolutionsCommutatorService::class => DefaultFactory::class,
            Service\Client::class => DefaultFactory::class,
        ],
        'aliases' => [
            AlgoApiSelectorInterface::class => AlgoServiceProvider::class,
            TranslationAlgoClientInterface::class => AlgoServiceProvider::class,
            CallSummarizationGetterInterface::class => AlgoServiceProvider::class,
        ],
    ],
];
