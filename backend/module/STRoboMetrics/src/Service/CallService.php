<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

use Carbon\Carbon;
use STRoboMetrics\Data\CallsTable;
use STRoboMetrics\Data\ApiCallsLogsTable;
use STRoboMetrics\Request\Call\CallLogsRequest;
use STRoboMetrics\Request\Call\SummaryRequest;

class CallService
{
    /**
     *
     * @param CallsTable $callsTable
     * @param ApiCallsLogsTable $apiCallsLogsTable
     * @param ChecklistFormatterService $checklistFormatterService
     */
    public function __construct(
        protected CallsTable $callsTable,
        protected ApiCallsLogsTable $apiCallsLogsTable,
        protected ChecklistFormatterService $checklistFormatterService,
    ) {
    }

    /**
     *
     * @param SummaryRequest $summaryRequest
     * @return array
     */
    public function getCallsSummary(SummaryRequest $summaryRequest): array
    {
        return $this->callsTable->getCallsSummary($summaryRequest);
    }

    /**
     *
     * @param SummaryRequest $summaryRequest
     * @return array
     */
    public function getChatCallsSummary(SummaryRequest $summaryRequest): array
    {
        return $this->callsTable->getChatCallsSummary($summaryRequest);
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function getBillingSummary(Carbon $startDate, Carbon $endDate): array
    {
        return $this->callsTable->getBillingSummary($startDate, $endDate);
    }

    public function getUsageStatistics(Carbon $startDate, Carbon $endDate): array
    {
        $callsStats = $this->callsTable->getCallsStatistics($startDate, $endDate) ?? [];
        $chatStats = $this->callsTable->getChatStatistics($startDate, $endDate) ?? [];
        $checklistsStats = $this->checklistFormatterService->formatChecklistsStatistics(
            $this->callsTable->getChecklistsStatistics($startDate, $endDate)
        );
        $summarizationStats = $this->callsTable->getSummarizationStatistics($startDate, $endDate) ?? [];

        return $this->checklistFormatterService->formatUsageStatistics(
            $callsStats,
            $chatStats,
            $checklistsStats,
            $summarizationStats
        );
    }

    /**
     *
     * @param \STRoboMetrics\Request\Call\CallLanguagesRequest $request
     * @return array
     */
    public function getCallLanguages(\STRoboMetrics\Request\Call\CallLanguagesRequest $request): array
    {
        return $this->callsTable->getCallHoursPerLanguages($request);
    }

    /**
     *
     * @param CallLogsRequest $request
     * @return array
     */
    public function getCallLogs(CallLogsRequest $request): array
    {
        return $this->apiCallsLogsTable->getCallLogs($request);
    }

    /**
     *
     * @param CallLogsRequest $request
     * @return array
     */
    public function getCallLogsByMessage(CallLogsRequest $request): array
    {
        return $this->apiCallsLogsTable->getCallLogsByMessage($request);
    }
}
