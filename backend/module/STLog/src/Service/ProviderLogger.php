<?php

declare(strict_types=1);

namespace STLog\Service;

trait ProviderLogger
{
    /**
     *
     * @var LogManagerWrapper
     */
    private ?LogManagerWrapper $logManagerWrapper = null;

    /**
     *
     * @param string|null $name
     * @return \Laminas\Log\Logger|\STLog\Service\LogManager
     */
    public function logger(?string $name = null): \Laminas\Log\Logger|\STLog\Service\LogManager
    {
        if (is_null($this->logManagerWrapper)) {
            $this->logManagerWrapper = new LogManagerWrapper();
        }
        $logManager = $this->logManagerWrapper::getStaticLogManager();
        if (is_null($logManager)) {
            return $logManager;
        }
        return $logManager->create($name);
    }
}
