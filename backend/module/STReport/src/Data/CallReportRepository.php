<?php

declare(strict_types=1);

namespace STReport\Data;

use Carbon\Carbon;
use STClickhouse\Entity\Pagination\CandidatePagination;

class CallReportRepository extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use \STClickhouse\Data\CallsWhereCondition;

    protected const CALLS_CSV_REPORT_BASE_URL_CALL_ID_PATTERN = '{call_id}';

    public function getCalls(
        CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): CandidatePagination {
        $processedFilters = $this->getProcessedFilters($companyId, $pagination->getFilter());
        $pagination->setFilter($processedFilters);

        $this->paginate(
            $this->getCallCandidatesSql($companyId, $roleId, $startDate, $endDate),
            'call_id',
            $pagination,
        );

        if ($pagination->getCount() === 0) {
            return $pagination;
        }

        // phpcs:disable
        $sql = '
            SELECT
                c.company_id company_id,
                c.call_id call_id,
                pc.role_id role_id,
                c.call_time call_time,
                c.call_type call_type,
                c.call_language call_language,
                c.call_status call_status,
                c.call_duration call_duration,
                c.paragraphs_count paragraphs_count,
                c.uploaded_time uploaded_time,
                c.is_transcribed is_transcribed,
                c.is_translated is_translated,
                c.is_analyzed is_analyzed,
                c.is_sent_to_transcribing is_sent_to_transcribing,
                c.original_file_name original_file_name,
                c.uploaded_user_id uploaded_user_id,
                uu.uploaded_user_name uploaded_user_name,
                c.agent_id agent_id,
                u.user_name agent_name,
                c.client_id client_id,
                cl.client_name client_name,
                cl.country client_country,
                cl.status client_status,
                cl.source client_source,
                cl.acquisition_date client_acquisition_date,
                cl.is_converted client_is_converted,
                cl.converted_date client_converted_date,
                cl.last_transaction_date client_last_transaction_date,
                cl.campaign_id client_campaign_id,
                cl.value client_value,
                c.call_origin call_origin,
                pc.is_reviewed is_reviewed,
                pc.is_partly_reviewed is_partly_reviewed,
                pc.reviewed_time reviewed_time,
                pc.reviewer_user_id reviewer_user_id,
                pc.event_ids event_ids,
                pc.event_category_ids event_category_ids,
                pc.score score,
                pc.risk_rank risk_rank,
                pc.reviewers reviewers,
                pc.fragments fragments,
                pc.conversation_type conversation_type,
                a.teams teams,
                ce.events events
            FROM
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'calls',
                            [
                                'company_id',
                                'call_id',
                            ],
                            'created',
                            [
                                'call_time',
                                'agent_id',
                                'client_id',
                                'call_origin',
                                'call_language',
                                'call_type',
                                'call_status',
                                'paragraphs_count',
                                'call_duration',
                                'uploaded_time',
                                'original_file_name',
                                'uploaded_user_id',
                                'is_transcribed',
                                'is_translated',
                                'is_analyzed',
                                'is_sent_to_transcribing',
                            ],
                            [
                                'company_id' => $companyId,
                                'call_id' => $pagination->getCandidateIds(),
                            ],
                        )
                        . '
                ) c
            LEFT JOIN
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'precalculated_calls',
                            [
                                'company_id',
                                'role_id',
                                'call_id',
                            ],
                            'created',
                            [
                                'call_duration',
                                'is_analyzed',
                                'is_reviewed',
                                'is_partly_reviewed',
                                'reviewed_time',
                                'reviewer_user_id',
                                'event_ids',
                                'event_category_ids',
                                'score',
                                'risk_rank',
                                'reviewers',
                                'fragments',
                                'conversation_type',
                                'created',
                            ],
                            [
                                'company_id' => $companyId,
                                'role_id' => $roleId,
                                'call_id' => $pagination->getCandidateIds(),
                            ],
                        )
                        . '
                ) pc
                ON c.call_id = pc.call_id
            LEFT JOIN
                (
                    SELECT 
                        user_id user_id, 
                        any(user_name) agent_name, 
                        groupArray(
                            map(
                                \'team_id\', toString(team_id), 
                                \'team_name\', team_name
                            )
                        ) teams
                    FROM 
                        dictionary(users_teams) 
                    WHERE 
                        company_id = ' . $companyId . '
                    GROUP BY 
                        user_id
                ) a
                ON a.user_id = c.agent_id
            LEFT JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(users)
                )
                u ON u.user_id = c.agent_id
            LEFT JOIN
                (
                    SELECT 
                        user_id user_id, 
                        user_name uploaded_user_name
                    FROM 
                        dictionary(users)
                ) uu
                ON uu.user_id = c.uploaded_user_id
            LEFT JOIN
                (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                            'clients',
                            [
                                'company_id',
                                'client_id',
                            ],
                            'created',
                            [
                                'client_name',
                                'country',
                                'status',
                                'source',
                                'acquisition_date',
                                'is_converted',
                                'converted_date',
                                'last_transaction_date',
                                'campaign_id',
                                'value',
                            ],
                            [
                                'company_id' => $companyId,
                                [
                                    'type' => 'expression',
                                    'value' => '
                                        client_id IN
                                        (
                                            SELECT DISTINCT
                                                client_id
                                            FROM
                                                calls
                                            WHERE 
                                                company_id = ' . $companyId . '
                                                AND call_id IN (\'' . implode('\',\'', $pagination->getCandidateIds()) . '\')
                                        )
                                    ',
                                ],
                            ]) . '
                ) cl
                ON cl.client_id = c.client_id
            LEFT JOIN
                (
                    SELECT
                        call_id,
                        groupArray(
                            map
                            (
                                \'event_id\', toString(event_id),
                                \'category_id\', toString(category_id),
                                \'event_name\', event_name,
                                \'category_name\', category_name,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(color_id),
                                \'fill_color_hex\', fill_color_hex,
                                \'outline_color_hex\', outline_color_hex,
                                \'count\', toString(count)
                            )
                        ) events
                    FROM
                        (
                            SELECT
                                call_id,
                                event_id,
                                event_category_id category_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                any(event_icon) event_icon,
                                any(event_color_id) color_id,
                                any(event_fill_color_hex) fill_color_hex,
                                any(event_outline_color_hex) outline_color_hex,
                                COUNT(*) count
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'event_category_id',
                                            'event_category_name',
                                            'event_name',
                                            'event_icon',
                                            'event_color_id',
                                            'event_fill_color_hex',
                                            'event_outline_color_hex',
                                            'event_is_deleted',
                                        ],
                                        [
                                            'company_id' => $companyId,
                                            'role_id' => $roleId,
                                            'call_id' => $pagination->getCandidateIds(),
                                        ],
                                    ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                call_id,
                                event_category_id,
                                event_id
                        )
                    GROUP BY
                        call_id
                ) ce
                ON ce.call_id = c.call_id
        ';
        $sql .= $this->getClient()->getSortSql($pagination->getParams());
        // phpcs:enable

        $result = $this->getClient()->selectAll($sql);
        $pagination->setResult($result);

        return $pagination;
    }

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return CandidatePagination
     */
    public function getCallsReport(
        CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): CandidatePagination {
        $eventIds = $pagination->getFilter()['event_id'] ?? [];
        $eventCategoryIds = $pagination->getFilter()['event_category_id'] ?? [];
        $processedFilters = $this->getProcessedFilters($companyId, $pagination->getFilter());
        $pagination->setFilter($processedFilters);

        $this->paginate(
            $this->getCallReportCandidatesSql($companyId, $roleId, $startDate, $endDate),
            'call_id',
            $pagination,
        );

        if ($pagination->getCount() === 0) {
            return $pagination;
        }

        $eventsFilter = $this->getEventsFilter($companyId, $roleId, $pagination->getCandidateIds(), $eventIds, $eventCategoryIds);

        // phpcs:disable
        $sql = '
            SELECT
                c.company_id company_id,
                c.call_id call_id,
                c.call_time call_time,
                c.call_type call_type,
                c.call_language call_language,
                c.call_status call_status,
                c.call_duration call_duration,
                c.paragraphs_count paragraphs_count,
                c.uploaded_time uploaded_time,
                c.is_transcribed is_transcribed,
                c.is_translated is_translated,
                c.is_analyzed is_analyzed,
                c.is_sent_to_transcribing is_sent_to_transcribing,
                c.original_file_name original_file_name,
                c.uploaded_user_id uploaded_user_id,
                uu.uploaded_user_name uploaded_user_name,
                c.agent_id agent_id,
                u.user_name agent_name,
                c.client_id client_id,
                cl.client_name client_name,
                cl.country client_country,
                cl.status client_status,
                cl.source client_source,
                cl.acquisition_date client_acquisition_date,
                cl.is_converted client_is_converted,
                cl.converted_date client_converted_date,
                cl.last_transaction_date client_last_transaction_date,
                cl.campaign_id client_campaign_id,
                cl.value client_value,
                c.call_origin call_origin,
                pc.is_reviewed is_reviewed,
                pc.is_partly_reviewed is_partly_reviewed,
                pc.reviewed_time reviewed_time,
                pc.reviewer_user_id reviewer_user_id,
                pc.event_ids event_ids,
                pc.event_category_ids event_category_ids,
                pc.score score,
                pc.risk_rank risk_rank,
                pc.reviewers reviewers,
                pc.fragments fragments,
                pc.conversation_type conversation_type,
                a.teams teams,
                ce.events events
            FROM
                (
                    ' .
                    $this->getFinalTableSqlUsingGroupBy(
                        'calls',
                        [
                            'company_id',
                            'call_id',
                        ],
                        'created',
                        [
                            'call_time',
                            'agent_id',
                            'client_id',
                            'call_origin',
                            'call_language',
                            'call_type',
                            'call_status',
                            'paragraphs_count',
                            'call_duration',
                            'uploaded_time',
                            'original_file_name',
                            'uploaded_user_id',
                            'is_transcribed',
                            'is_translated',
                            'is_analyzed',
                            'is_sent_to_transcribing',
                        ],
                        [
                            'company_id' => $companyId,
                            'call_id' => $pagination->getCandidateIds(),
                        ],
                    )
                    . '
                ) c
            LEFT JOIN
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'precalculated_calls',
                            [
                                'company_id',
                                'role_id',
                                'call_id',
                            ],
                            'created',
                            [
                                'call_duration',
                                'is_analyzed',
                                'is_reviewed',
                                'is_partly_reviewed',
                                'reviewed_time',
                                'reviewer_user_id',
                                'event_ids',
                                'event_category_ids',
                                'score',
                                'risk_rank',
                                'reviewers',
                                'fragments',
                                'conversation_type',
                                'created',
                            ],
                            [
                                'company_id' => $companyId,
                                'role_id' => $roleId,
                                'call_id' => $pagination->getCandidateIds(),
                            ],
                        )
                        . '
                ) pc
                ON c.call_id = pc.call_id
            LEFT JOIN
                (
                    SELECT 
                        user_id user_id, 
                        any(user_name) agent_name, 
                        groupArray(
                            map(
                                \'team_id\', toString(team_id), 
                                \'team_name\', team_name
                            )
                        ) teams
                    FROM 
                        dictionary(users_teams) 
                    WHERE 
                        company_id = ' . $companyId . '
                    GROUP BY 
                        user_id
                ) a
                ON a.user_id = c.agent_id
            LEFT JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(users)
                )
                u ON u.user_id = c.agent_id
            LEFT JOIN
                (
                    SELECT 
                        user_id user_id, 
                        user_name uploaded_user_name
                    FROM 
                        dictionary(users)
                ) uu
                ON uu.user_id = c.uploaded_user_id
            LEFT JOIN
                (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                        'clients',
                        [
                            'company_id',
                            'client_id',
                        ],
                        'created',
                        [
                            'client_name',
                            'country',
                            'status',
                            'source',
                            'acquisition_date',
                            'is_converted',
                            'converted_date',
                            'last_transaction_date',
                            'campaign_id',
                            'value',
                        ],
                        [
                            'company_id' => $companyId,
                            [
                                'type' => 'expression',
                                'value' => '
                                    client_id IN
                                    (
                                        SELECT DISTINCT
                                            client_id
                                        FROM
                                            calls
                                        WHERE 
                                            company_id = ' . $companyId . '
                                            AND call_id IN (\'' . implode('\',\'', $pagination->getCandidateIds()) . '\')
                                    )
                                ',
                            ],
                        ]
                    ) . '
                ) cl
                ON cl.client_id = c.client_id
            LEFT JOIN
                (
                    SELECT
                        call_id,
                        groupArray(
                            map
                            (
                                \'event_id\', toString(event_id),
                                \'category_id\', toString(category_id),
                                \'event_name\', event_name,
                                \'paragraph_number\', toString(paragraph),
                                \'paragraph_start_time\', toString(paragraph_start_time),
                                \'category_name\', category_name,
                                \'event_icon\', event_icon,
                                \'event_color_id\', toString(event_color_id),
                                \'fill_color_hex\', fill_color_hex,
                                \'outline_color_hex\', outline_color_hex,
                                \'event_color_priority\', toString(event_color_priority),
                                \'paragraph_speaker_role\', paragraph_speaker_role,
                                \'event_highlight\', event_highlight,
                                \'event_en_highlight\', event_en_highlight,
                                \'event_text\', event_text,
                                \'event_en_text\', event_en_text
                            )
                        ) events
                    FROM
                        (
                            SELECT
                                call_id,
                                event_id,
                                paragraph,
                                any(paragraph_start_time) paragraph_start_time,
                                any(event_category_id) category_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                any(event_icon) event_icon,
                                any(event_color_id) event_color_id,
                                any(event_fill_color_hex) fill_color_hex,
                                any(event_outline_color_hex) outline_color_hex,
                                any(event_color_priority) event_color_priority,
                                any(paragraph_speaker_role) paragraph_speaker_role,
                                any(event_highlight) event_highlight,
                                any(event_en_highlight) event_en_highlight,
                                any(event_text) event_text,
                                any(event_en_text) event_en_text
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'event_category_id',
                                            'event_category_name',
                                            'paragraph_start_time',
                                            'event_name',
                                            'event_icon',
                                            'event_color_id',
                                            'event_fill_color_hex',
                                            'event_outline_color_hex',
                                            'event_color_priority',
                                            'paragraph_speaker_role',
                                            'event_highlight',
                                            'event_en_highlight',
                                            'event_text',
                                            'event_en_text',
                                            'event_is_deleted',
                                        ],
                                        $eventsFilter,
                                    ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                call_id,
                                event_id,
                                paragraph
                            ORDER BY
                                paragraph_start_time
                        )
                    GROUP BY
                        call_id
                ) ce
                ON ce.call_id = c.call_id
        ';
        // phpcs:enable
        $sql .= $this->getClient()->getSortSql($pagination->getParams());

        $result = $this->getClient()->selectAll($sql);
        $pagination->setResult($result);

        return $pagination;
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param array $filters
     * @param array $columns
     * @param string $baseCallUrl
     * @return string
     */
    public function getCallsCsvReport(
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        array $filters = [],
        array $columns = [],
        string $baseCallUrl = '',
    ): string {
        $eventIds = $filters['event_id'] ?? [];
        $eventCategoryIds = $filters['event_category_id'] ?? [];
        $processedFilters = $this->getProcessedFilters($companyId, $filters);
        $candidateIdsSql = $this->getCallReportCandidatesSql($companyId, $roleId, $startDate, $endDate) . $this->getClient()->convertFilterToSql($processedFilters);
        $candidateIds = $this->getClient()->selectColumn($candidateIdsSql, 'call_id');
        if (count($candidateIds) === 0) {
            return '';
        }

        $eventsFilter = $this->getEventsFilter($companyId, $roleId, $candidateIds, $eventIds, $eventCategoryIds);

        // phpcs:disable
        $sql = '
            SELECT
                c.call_id call_id,
                c.call_time call_time,
                c.call_type call_type,
                c.call_language call_language,
                c.call_status call_status,
                c.call_duration call_duration,
                c.paragraphs_count paragraphs_count,
                c.uploaded_time uploaded_time,
                c.original_file_name original_file_name,
                c.uploaded_user_id uploaded_user_id,
                uu.uploaded_user_name uploaded_user_name,
                u.user_name agent_name,
                c.client_id client_id,
                cl.client_name client_name,
                cl.country client_country,
                cl.status client_status,
                cl.source client_source,
                cl.acquisition_date client_acquisition_date,
                cl.is_converted client_is_converted,
                cl.converted_date client_converted_date,
                cl.last_transaction_date client_last_transaction_date,
                cl.campaign_id client_campaign_id,
                cl.value client_value,
                c.call_origin call_origin,
                pc.is_reviewed is_reviewed,
                pc.is_partly_reviewed is_partly_reviewed,
                pc.reviewed_time reviewed_time,
                pc.reviewer_user_id reviewer_user_id,
                pc.score score,
                pc.conversation_type conversation_type,
                pc.risk_rank risk_rank,
                arrayStringConcat(arrayMap(reviewer -> (reviewer[\'name\']), pc.reviewers), \',\') reviewers,
                a.teams teams,
                ce.events events,
                ce.en_events en_events,
                replaceOne(\'' .  $baseCallUrl . '\', \'' . static::CALLS_CSV_REPORT_BASE_URL_CALL_ID_PATTERN. '\', c.call_id) url
            FROM
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'calls',
                            [
                                'company_id',
                                'call_id',
                            ],
                            'created',
                            [
                                'call_time',
                                'agent_id',
                                'client_id',
                                'call_origin',
                                'call_language',
                                'call_type',
                                'call_status',
                                'paragraphs_count',
                                'call_duration',
                                'uploaded_time',
                                'original_file_name',
                                'uploaded_user_id',
                                'is_transcribed',
                                'is_translated',
                                'is_analyzed',
                                'is_sent_to_transcribing',
                            ],
                            [
                                'company_id' => $companyId,
                                [
                                    'type' => 'expression',
                                    'value' => 'call_id IN (' . $candidateIdsSql . ')',
                                ],
                            ],
                        )
                        . '
                ) c
            LEFT JOIN
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'precalculated_calls',
                            [
                                'company_id',
                                'role_id',
                                'call_id',
                            ],
                            'created',
                            [
                                'call_duration',
                                'is_analyzed',
                                'is_reviewed',
                                'is_partly_reviewed',
                                'reviewed_time',
                                'reviewer_user_id',
                                'event_ids',
                                'event_category_ids',
                                'score',
                                'risk_rank',
                                'reviewers',
                                'fragments',
                                'conversation_type',
                                'created',
                            ],
                            [
                                'company_id' => $companyId,
                                'role_id' => $roleId,
                                [
                                    'type' => 'expression',
                                    'value' => 'call_id IN (' . $candidateIdsSql . ')',
                                ],
                            ],
                        )
                        . '
                ) pc
                ON c.call_id = pc.call_id
            LEFT JOIN
                (
                    SELECT 
                        user_id user_id, 
                        any(user_name) agent_name, 
                        arrayStringConcat(
                            arrayReduce(\'groupUniqArray\',
                                arrayFilter(
                                    team_name -> trim(team_name) <> \'\',
                                    groupArray(team_name)
                                )
                            ),
                            \', \'
                        ) teams
                    FROM 
                        dictionary(users_teams) 
                    WHERE 
                        company_id = ' . $companyId . '
                    GROUP BY 
                        user_id
                ) a
                ON a.user_id = c.agent_id
            LEFT JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(users)
                )
                u ON u.user_id = c.agent_id
            LEFT JOIN
                (
                    SELECT 
                        user_id user_id, 
                        user_name uploaded_user_name
                    FROM 
                        dictionary(users)
                ) uu
                ON uu.user_id = c.uploaded_user_id
            LEFT JOIN
                (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                        'clients',
                        [
                            'company_id',
                            'client_id',
                        ],
                        'created',
                        [
                            'client_name',
                            'country',
                            'status',
                            'source',
                            'acquisition_date',
                            'is_converted',
                            'converted_date',
                            'last_transaction_date',
                            'campaign_id',
                            'value',
                        ],
                        [
                            'company_id' => $companyId,
                            [
                                'type' => 'expression',
                                'value' => '
                                    client_id IN
                                    (
                                        SELECT DISTINCT
                                            client_id
                                        FROM
                                            calls
                                        WHERE 
                                            company_id = ' . $companyId . '
                                            AND call_id IN (' . $candidateIdsSql . ')
                                    )
                                ',
                            ],
                        ]
                    ) . '
                ) cl
                ON cl.client_id = c.client_id
            LEFT JOIN
                (
                    SELECT
                        call_id,
                        arrayStringConcat(
                            groupArray
                            (
                                concat
                                (
                                    category_name
                                    , \': \'
                                    , event_name
                                    , \' \'
                                    , formatDateTime(CAST(toInt64(paragraph_start_time) AS DATETIME), \'%H:%i:%s\')
                                    , \': \'
                                    , event_text
                                )
                            ), \'\n\n\'
                        ) events,
                        arrayStringConcat(
                            groupArray(
                                concat
                                (
                                    category_name
                                    , \': \'
                                    , event_name
                                    , \' \'
                                    , formatDateTime(CAST(toInt64(paragraph_start_time) AS DATETIME), \'%H:%i:%s\')
                                    , \': \'
                                    , event_en_text
                                )
                            ), \'\n\n\'
                        )
                        en_events
                    FROM
                        (
                            SELECT
                                call_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                any(paragraph_start_time) paragraph_start_time,
                                any(event_text) event_text,
                                any(event_en_text) event_en_text
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'event_category_name',
                                            'event_name',
                                            'paragraph_start_time',
                                            'event_text',
                                            'event_en_text',
                                            'event_is_deleted',
                                        ],
                                        $eventsFilter,
                                    ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                call_id,
                                paragraph,
                                event_id
                        )
                    GROUP BY
                        call_id
                ) ce
                ON ce.call_id = c.call_id
        ';
        // phpcs:enable

        $sql = '
            SELECT
                ' . $this->getClient()->getSelectString($columns) . '
            FROM
            (
                ' . $sql . '
            )
            
        ';
        return $this->getClient()->selectCsv($sql);
    }

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param string $search
     * @return CandidatePagination
     */
    public function searchCalls(
        CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        string $search,
    ): CandidatePagination {
        $processedFilters = $this->getProcessedFilters($companyId, $pagination->getFilter());
        $pagination->setFilter($processedFilters);

        $this->paginate(
            $this->getCallCandidatesSql($companyId, $roleId, search: $search),
            'call_id',
            $pagination,
        );

        if ($pagination->getCount() === 0) {
            return $pagination;
        }

        // phpcs:disable
        $sql = '
            SELECT
                c.company_id company_id,
                c.call_id call_id,
                pc.role_id role_id,
                c.call_time call_time,
                c.call_type call_type,
                c.call_language call_language,
                c.call_status call_status,
                c.call_duration call_duration,
                c.paragraphs_count paragraphs_count,
                c.uploaded_time uploaded_time,
                c.is_transcribed is_transcribed,
                c.is_translated is_translated,
                c.is_analyzed is_analyzed,
                c.agent_id agent_id,
                u.user_name agent_name,
                c.client_id client_id,
                cl.client_name client_name,
                cl.country client_country,
                cl.status client_status,
                cl.source client_source,
                cl.acquisition_date client_acquisition_date,
                cl.is_converted client_is_converted,
                cl.converted_date client_converted_date,
                cl.last_transaction_date client_last_transaction_date,
                cl.campaign_id client_campaign_id,
                cl.value client_value,
                c.call_origin call_origin,
                pc.is_reviewed is_reviewed,
                pc.is_partly_reviewed is_partly_reviewed,
                pc.reviewed_time reviewed_time,
                pc.reviewer_user_id reviewer_user_id,
                pc.event_ids event_ids,
                pc.event_category_ids event_category_ids,
                pc.score score,
                pc.risk_rank risk_rank,
                pc.reviewers reviewers,
                pc.fragments fragments,
                pc.conversation_type conversation_type,
                a.teams teams,
                ce.events events
            FROM
                (
                    ' .
                    $this->getFinalTableSqlUsingGroupBy(
                        'calls',
                        [
                            'company_id',
                            'call_id',
                        ],
                        'created',
                        [
                            'client_id',
                            'agent_id',
                            'call_time',
                            'call_type',
                            'call_origin',
                            'call_language',
                            'call_status',
                            'paragraphs_count',
                            'call_duration',
                            'uploaded_time',
                            'is_transcribed',
                            'is_translated',
                            'is_analyzed',
                        ],
                        [
                            'company_id' => $companyId,
                            'call_id' => $pagination->getCandidateIds(),
                        ],
                    )
                    . '
                ) c
            LEFT JOIN
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'precalculated_calls',
                            [
                                'company_id',
                                'role_id',
                                'call_id',
                            ],
                            'created',
                            [
                                'is_reviewed',
                                'is_partly_reviewed',
                                'reviewed_time',
                                'reviewer_user_id',
                                'event_ids',
                                'event_category_ids',
                                'score',
                                'risk_rank',
                                'reviewers',
                                'fragments',
                                'conversation_type',
                                'created',
                            ],
                            [
                                'company_id' => $companyId,
                                'role_id' => $roleId,
                                'call_id' => $pagination->getCandidateIds(),
                            ],
                        )
                    . '
                ) pc
                ON c.call_id = pc.call_id
            LEFT JOIN
                (
                    SELECT 
                        user_id user_id, 
                        any(user_name) agent_name, 
                        groupArray(
                            map(
                                \'team_id\', toString(team_id), 
                                \'team_name\', team_name
                            )
                        ) teams
                    FROM 
                        dictionary(users_teams) 
                    WHERE 
                        company_id = ' . $companyId . '
                    GROUP BY 
                        user_id
                ) a
                ON a.user_id = c.agent_id
            LEFT JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(users)
                )
                u ON u.user_id = c.agent_id
            LEFT JOIN
                (
                    ' . $this->getFinalTableSqlUsingGroupBy('clients', [
                            'company_id',
                            'client_id',
                        ], 'created', [
                            'client_name',
                            'country',
                            'status',
                            'source',
                            'acquisition_date',
                            'is_converted',
                            'converted_date',
                            'last_transaction_date',
                            'campaign_id',
                            'value',
                        ], [
                            'company_id' => $companyId,
                            [
                                'type' => 'expression',
                                'value' => '
                                    client_id IN
                                    (
                                        SELECT DISTINCT
                                            client_id
                                        FROM
                                            calls
                                        WHERE 
                                            company_id = ' . $companyId . '
                                            AND call_id IN (\'' . implode('\',\'', $pagination->getCandidateIds()) . '\')
                                    )
                                ',
                            ],
                        ]) . '
                ) cl
                ON cl.client_id = c.client_id
            LEFT JOIN
                (
                    SELECT
                        call_id,
                        groupArray(
                            map
                            (
                                \'event_id\', toString(event_id),
                                \'category_id\', toString(category_id),
                                \'event_name\', event_name,
                                \'category_name\', category_name,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(color_id),
                                \'fill_color_hex\', fill_color_hex,
                                \'outline_color_hex\', outline_color_hex,
                                \'event_highlight\', event_highlight,
                                \'event_en_highlight\', event_en_highlight,
                                \'event_text\', event_text,
                                \'event_en_text\', event_en_text,
                                \'count\', toString(count)
                            )
                        ) events
                    FROM
                        (
                            SELECT
                                call_id,
                                event_id,
                                any(event_category_id) category_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                any(event_icon) event_icon,
                                any(event_color_id) color_id,
                                any(event_fill_color_hex) fill_color_hex,
                                any(event_outline_color_hex) outline_color_hex,
                                any(event_highlight) event_highlight,
                                any(event_en_highlight) event_en_highlight,
                                any(event_text) event_text,
                                any(event_en_text) event_en_text,
                                COUNT(*) count
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'event_category_id',
                                            'event_category_name',
                                            'event_name',
                                            'event_icon',
                                            'event_color_id',
                                            'event_fill_color_hex',
                                            'event_outline_color_hex',
                                            'event_highlight',
                                            'event_en_highlight',
                                            'event_text',
                                            'event_en_text',
                                            'event_is_deleted',
                                        ],
                                        [
                                            'company_id' => $companyId,
                                            'role_id' => $roleId,
                                            'call_id' => $pagination->getCandidateIds(),
                                        ],
                                    ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                call_id,
                                event_id,
                                paragraph
                        )
                    GROUP BY
                        call_id
                ) ce
                ON ce.call_id = c.call_id
        ';
        $sql .= $this->getClient()->getSortSql($pagination->getParams());
        // phpcs:enable

        $result = $this->getClient()->selectAll($sql);
        $pagination->setResult($result);

        return $pagination;
    }

    /**
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    public function getFilterValuesRanges(
        int $companyId,
        ?Carbon $startDate,
        ?Carbon $endDate,
    ): array {
        $sql = <<<SQL
            SELECT 
                min(c.call_duration) min_call_duration,
                max(c.call_duration) max_call_duration,
                min(cl.value) min_client_value,
                max(cl.value) max_client_value,
                min(score) min_event_score_value,
                max(score) max_event_score_value
            FROM ({$this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'company_id',
                    'call_id',
                ],
                'created',
                [
                    'call_duration',
                    'call_time',
                    'client_id'
                ],
                $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
            )}) c
            LEFT JOIN ({$this->getFinalTableSqlUsingGroupBy(
                'precalculated_calls',
                [
                    'company_id',
                    'call_id',
                    'role_id',
                ],
                'created',
                [
                    'score',
                    'call_time'
                ],
                $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
            )}) pc ON pc.call_id = c.call_id
            LEFT JOIN ({$this->getFinalTableSqlUsingGroupBy(
                'clients',
                [
                    'company_id',
                    'client_id',
                ],
                'created',
                [
                    'value'
                ],
                $this->getCallsWhereConditionsFromMainParams($companyId),
            )}) cl ON cl.client_id = c.client_id  
        SQL;

        return $this->getClient()->selectOne($sql);
    }

    /**
     * @param int $companyId
     * @param array $filters
     * @return array
     */
    protected function getProcessedFilters(int $companyId, array $filters): array
    {
        $filters['is_deleted'] = [
            'is_deleted' => 0,
        ];
        if (isset($filters['min_call_duration'])) {
            $filters['min_call_duration'] = [
                'column' => 'call_duration',
                'value' => (int) $filters['min_call_duration'],
                'type' => 'compare',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_call_duration'])) {
            $filters['max_call_duration'] = [
                'column' => 'call_duration',
                'value' => (int) $filters['max_call_duration'],
                'type' => 'compare',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_score'])) {
            $filters['min_score'] = [
                'column' => 'score',
                'value' => (int) $filters['min_score'],
                'type' => 'compare',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_score'])) {
            $filters['max_score'] = [
                'column' => 'score',
                'value' => (int) $filters['max_score'],
                'type' => 'compare',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_reviewed_time'])) {
            $filters['min_reviewed_time'] = [
                'column' => 'reviewed_time',
                'value' => Carbon::parse($filters['min_reviewed_time'])->startOfDay(),
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_reviewed_time'])) {
            $filters['max_reviewed_time'] = [
                'column' => 'reviewed_time',
                'value' => Carbon::parse($filters['max_reviewed_time'])->endOfDay(),
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_client_value'])) {
            $filters['min_client_value'] = [
                'column' => 'client_value',
                'value' => (float) $filters['min_client_value'],
                'type' => 'compare',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_client_value'])) {
            $filters['max_client_value'] = [
                'column' => 'client_value',
                'value' => (float) $filters['max_client_value'],
                'type' => 'compare',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_client_last_transaction_date'])) {
            $filters['min_client_last_transaction_date'] = [
                'column' => 'client_last_transaction_date',
                'value' => Carbon::parse($filters['min_client_last_transaction_date'])->startOfDay(),
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_client_last_transaction_date'])) {
            $filters['max_client_last_transaction_date'] = [
                'column' => 'client_last_transaction_date',
                'value' => Carbon::parse($filters['max_client_last_transaction_date'])->endOfDay(),
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_client_converted_date'])) {
            $filters['min_client_converted_date'] = [
                'column' => 'client_converted_date',
                'value' => Carbon::parse($filters['min_client_converted_date'])->startOfDay(),
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_client_converted_date'])) {
            $filters['max_client_converted_date'] = [
                'column' => 'client_converted_date',
                'value' => Carbon::parse($filters['max_client_converted_date'])->endOfDay(),
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_client_acquisition_date'])) {
            $filters['min_client_acquisition_date'] = [
                'column' => 'client_acquisition_date',
                'value' => Carbon::parse($filters['min_client_acquisition_date'])->startOfDay(),
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_client_acquisition_date'])) {
            $filters['max_client_acquisition_date'] = [
                'column' => 'client_acquisition_date',
                'value' => Carbon::parse($filters['max_client_acquisition_date'])->endOfDay(),
                'type' => 'date',
                'compare' => '<=',
            ];
        }

        if (isset($filters['conversation_type'])) {
            $filters['conversation_type'] = [
                'column' => 'conversation_type',
                'value' => $filters['conversation_type'],
                'type' => 'compare',
                'compare' => '=',
            ];
        }

        $filters['reviewer_id'] = isset($filters['reviewer_id']) && is_array($filters['reviewer_id']) && count($filters['reviewer_id']) > 0
            ? [
                'value' => 'hasAny(arrayMap(reviewer -> (toInt32(reviewer[\'id\'])), reviewers), [' . implode(',', $filters['reviewer_id']) . '])',
                'type' => 'expression',
            ]
            : [];

        $filters['event_id'] = isset($filters['event_id']) && is_array($filters['event_id']) && count($filters['event_id']) > 0
            ? [
                'value' => 'hasAny(event_ids, [' . implode(',', $filters['event_id']) . '])',
                'type' => 'expression',
            ]
            : [];
        $filters['exclude_event_id'] = isset($filters['exclude_event_id']) && is_array($filters['exclude_event_id']) && count($filters['exclude_event_id']) > 0
            ? [
                'value' => 'NOT hasAny(event_ids, [' . implode(',', $filters['exclude_event_id']) . '])',
                'type' => 'expression',
            ]
            : [];
        $filters['event_category_id'] = isset($filters['event_category_id']) && is_array($filters['event_category_id']) && count($filters['event_category_id']) > 0
            ? [
                'value' => 'hasAny(event_category_ids, [' . implode(',', $filters['event_category_id']) . '])',
                'type' => 'expression',
            ]
            : [];
        $filters['team_id'] = isset($filters['team_id']) && is_array($filters['team_id']) && count($filters['team_id']) > 0
            ? [
                'value' => 'agent_id IN
                    (
                        SELECT 
                            user_id
                        FROM 
                            dictionary(users_teams) 
                        WHERE
                            company_id = ' . $companyId . '
                            AND team_id IN (' . implode(',', $filters['team_id']) . ')
                    )
                ',
                'type' => 'expression',
            ]
            : [];
        $filters['has_comments_only'] = isset($filters['has_comments_only'])
            ? [
                'value' => 'call_id IN
                    (
                        SELECT
                            call_id
                        FROM
                            (
                                SELECT
                                    call_id,
                                    MAX(is_unread) is_unread
                                FROM
                                (
                                    ' . $this->getFinalTableSqlUsingGroupBy('calls_comments_notifications', [
                                        'comment_id',
                                        'call_id',
                                    ], 'created', [
                                        'is_unread',
                                    ], [
                                        'company_id' => $companyId,
                                        'user_id' => $filters['has_comments_only']['user_id'],
                                    ]) . '
                                )
                                GROUP BY
                                    call_id
                            )
                    ' .
                        (
                            isset($filters['has_comments_only']['has_unread_comments'])
                                ? '
                            WHERE
                                 is_unread = ' . $filters['has_comments_only']['has_unread_comments'] . '
                                '
                                : ''
                        ) . '
                    )
                ',
                'type' => 'expression',
            ]
            : [];

        return $filters;
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param string|null $search
     * @return string
     */
    protected function getCallCandidatesSql(
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        string $search = null
    ): string {
        return '
            SELECT DISTINCT
                c.call_id call_id
            FROM
                (
                    ' .
                    // phpcs:disable
                    $this->getFinalTableSqlUsingGroupBy
                    (
                        'calls',
                        [
                            'company_id',
                            'call_id',
                            'call_type',
                            'call_origin',
                            'call_language',
                            'client_id',
                        ],
                        'created',
                        [
                            'is_deleted',
                            'uploaded_time',
                        ],
                        $this->getInternalFilters($companyId, $startDate, $endDate, search: $search),
                    )
                    // phpcs:enable
                    . '
                ) c
            LEFT JOIN
                (
                    ' .
                    // phpcs:disable
                    $this->getFinalTableSqlUsingGroupBy
                    (
                        'precalculated_calls',
                        [
                            'company_id',
                            'call_id',
                            'role_id',
                        ],
                        'created',
                        [
                            'call_time',
                            'agent_id',
                            'call_status',
                            'call_duration',
                            'is_analyzed',
                            'is_reviewed',
                            'is_partly_reviewed',
                            'reviewed_time',
                            'reviewer_user_id',
                            'event_ids',
                            'event_category_ids',
                            'score',
                            'risk_rank',
                            'reviewers',
                            'fragments',
                            'conversation_type',
                            'event_ids',
                            'event_category_ids',
                        ],
                        $this->getInternalFilters($companyId, $startDate, $endDate, $roleId, $search),
                    )
                    // phpcs:enable
                    . '
                ) pc
                ON pc.call_id = c.call_id
                AND pc.company_id = c.company_id
        ';
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param string|null $search
     * @return string
     */
    protected function getCallReportCandidatesSql(
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        string $search = null
    ): string {
        return '
            SELECT DISTINCT
                call_id
            FROM
                (
                    SELECT
                        c.call_id call_id,
                        c.company_id company_id,
                        c.call_type call_type,
                        c.call_origin call_origin,
                        c.call_language call_language,
                        c.client_id client_id,
                        c.agent_id agent_id,
                        c.is_analyzed is_analyzed,
                        c.call_status call_status,
                        c.call_duration call_duration,
                        c.is_deleted is_deleted,
                        cl.client_name client_name,
                        cl.client_country client_country,
                        cl.client_status client_status,
                        cl.client_source client_source,
                        cl.client_is_converted client_is_converted,
                        cl.client_campaign_id client_campaign_id,
                        cl.client_value client_value,
                        cl.client_last_transaction_date client_last_transaction_date,
                        cl.client_converted_date client_converted_date,
                        cl.client_acquisition_date client_acquisition_date,
                        pc.call_time call_time,
                        pc.is_reviewed is_reviewed,
                        pc.is_partly_reviewed is_partly_reviewed,
                        pc.reviewed_time reviewed_time,
                        pc.reviewer_user_id reviewer_user_id,
                        pc.event_ids event_ids,
                        pc.event_category_ids event_category_ids,
                        pc.score score,
                        pc.risk_rank risk_rank,
                        pc.reviewers reviewers,
                        pc.fragments fragments,
                        pc.conversation_type conversation_type,
                        a.team_name,
                        u.user_name
                    FROM
                        (
                            ' .
                            // phpcs:disable
                            $this->getFinalTableSqlUsingGroupBy
                            (
                                'calls',
                                [
                                    'company_id',
                                    'call_id',
                                    'call_type',
                                    'call_origin',
                                    'call_language',
                                    'client_id',
                                    'agent_id',
                                    'is_analyzed',
                                    'call_status',
                                    'call_duration',
                                ],
                                'created',
                                [
                                    'is_deleted',
                                    'uploaded_time',
                                ],
                                $this->getInternalFilters($companyId, $startDate, $endDate, search: $search),
                            )
                            // phpcs:enable
                            . '
                        ) c
                    LEFT JOIN
                        (
                            SELECT
                                company_id company_id,
                                client_id client_id,
                                last_value_respect_nulls(client_name) client_name,
                                last_value_respect_nulls(country) client_country,
                                last_value_respect_nulls(status) client_status,
                                last_value_respect_nulls(source) client_source,
                                last_value_respect_nulls(is_converted) client_is_converted,
                                last_value_respect_nulls(campaign_id) client_campaign_id,
                                last_value_respect_nulls(value) client_value,
                                last_value_respect_nulls(last_transaction_date) client_last_transaction_date,
                                last_value_respect_nulls(converted_date) client_converted_date,
                                last_value_respect_nulls(acquisition_date) client_acquisition_date
                            FROM
                                (
                                ' .
                                    $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_clients',
                                        [
                                            'date',
                                            'company_id',
                                            'client_id',
                                            'role_id',
                                        ],
                                        'created',
                                        [
                                            'client_name',
                                            'country',
                                            'status',
                                            'source',
                                            'is_converted',
                                            'campaign_id',
                                            'value',
                                            'last_transaction_date',
                                            'converted_date',
                                            'acquisition_date',
                                            'created',
                                        ],
                                        $this->getInternalClientFilters($companyId, $startDate, $endDate, $roleId),
                                    ) . '
                                    ORDER BY
                                        created
                                )
                            GROUP BY
                                company_id,
                                role_id,
                                client_id
                        ) cl
                        ON cl.client_id = c.client_id
                        AND cl.company_id = c.company_id
                    LEFT JOIN
                        (
                            SELECT 
                                user_id user_id, 
                                any(user_name) agent_name, 
                                arrayStringConcat
                                (
                                    groupArray
                                    (
                                        team_name
                                    )
                                ) team_name
                            FROM 
                                dictionary(users_teams) 
                            WHERE 
                                company_id = ' . $companyId . '
                            GROUP BY 
                                user_id
                        ) a
                        ON a.user_id = c.agent_id
                    LEFT JOIN 
                        (
                            SELECT
                                *
                            FROM
                                dictionary(users)
                        ) u
                        ON u.user_id = c.agent_id
                    LEFT JOIN
                        (
                            ' .
                            // phpcs:disable
                            $this->getFinalTableSqlUsingGroupBy
                            (
                                'precalculated_calls',
                                [
                                    'company_id',
                                    'call_id',
                                    'role_id',
                                ],
                                'created',
                                [
                                    'call_time',
                                    'client_id',
                                    'is_reviewed',
                                    'is_partly_reviewed',
                                    'reviewed_time',
                                    'reviewer_user_id',
                                    'event_ids',
                                    'event_category_ids',
                                    'score',
                                    'risk_rank',
                                    'conversation_type',
                                    'reviewers',
                                    'fragments',
                                ],
                                $this->getInternalFilters($companyId, $startDate, $endDate, $roleId, $search),
                            )
                            // phpcs:enable
                            . '
                        ) pc
                        ON pc.call_id = c.call_id
                        AND pc.company_id = c.company_id
                )
        ';
    }

    /**
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int|null $roleId
     * @param string|null $search
     * @return int[]
     */
    protected function getInternalFilters(int $companyId, ?Carbon $startDate, ?Carbon $endDate, ?int $roleId = null, string $search = null): array
    {
        $internalFilter = [
            'company_id' => $companyId,
        ];
        if (is_int($roleId) && $roleId > 0) {
            $internalFilter['role_id'] = $roleId;
        }
        if ($startDate instanceof Carbon) {
            $internalFilter['start_date'] = [
                'column' => 'call_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if ($endDate instanceof Carbon) {
            $internalFilter['end_date'] = [
                'column' => 'call_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (!empty($search)) {
            $internalFilter['call_search'] = [
                'column' => 'call_id',
                'value' => $search,
                'type' => 'like',
            ];
        }
        return $internalFilter;
    }

    /**
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int|null $roleId
     * @param string|null $search
     * @return int[]
     */
    protected function getInternalClientFilters(int $companyId, ?Carbon $startDate, ?Carbon $endDate, ?int $roleId = null, string $search = null): array
    {
        $internalFilter = [
            'company_id' => $companyId,
        ];
        if (is_int($roleId) && $roleId > 0) {
            $internalFilter['role_id'] = $roleId;
        }
        if ($startDate instanceof Carbon) {
            $internalFilter['start_date'] = [
                'column' => 'date',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if ($endDate instanceof Carbon) {
            $internalFilter['end_date'] = [
                'column' => 'date',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        return $internalFilter;
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param array $candidateIds
     * @param array $eventIds
     * @param array $eventCategoryIds
     * @return array
     */
    protected function getEventsFilter(int $companyId, int $roleId, array $candidateIds, array $eventIds, array $eventCategoryIds): array
    {
        $filter = [
            'company_id' => $companyId,
            'role_id' => $roleId,
            'call_id' => $candidateIds,
        ];

        $eventsSubFilter = [];
        if (!empty($eventIds)) {
            $eventsSubFilter[] = 'event_id IN (' . implode(',', $eventIds) . ')';
        }
        if (!empty($eventCategoryIds)) {
            $eventsSubFilter[] = 'event_category_id IN (' . implode(',', $eventCategoryIds) . ')';
        }
        if (!empty($eventsSubFilter)) {
            $filter[] = [
                'type' => 'expression',
                'value' => implode(' OR ', $eventsSubFilter),
            ];
        }

        return $filter;
    }
}
