<?php

namespace STCompany\Validator\Checklist;

use <PERSON><PERSON>\Validator\NumberComparison;
use <PERSON>inas\Validator\StringLength;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\ChecklistsTable;
use ST<PERSON>ompany\Entity\Checklist\Checklist;
use STLib\Validator\Validator;
use STUser\Entity\User;

class SaveChecklistValidator extends Validator
{
    public function __construct(
        private readonly ChecklistsTable $checklistsTable
    ) {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();
        $companyId = $input['company_id'];
        $checklistName = $input['checklist_name'] ?? null;
        $checklistId = $input['checklist_id'] ?? null;
        $callsScope = $input['calls_scope'] ?? null;

        /**
         * @var User $user
         */
        $user = $input['user'];

        if (!is_null($checklistId)) {
            try {
                $this->checklistsTable->getChecklist($checklistId, $companyId);
            } catch (NotFoundApiException $e) {
                $this->addError('checklist', 'Checklist not found');
            }
        } elseif (!$user->isGlobalAdmin()) {
            $existingChecklists = $this->checklistsTable->getCompanyChecklists($companyId);
            if (!$existingChecklists->isEmpty()) {
                $this->addError(
                    'checklist',
                    'You can\'t create more than one Flow yourself. If you need it, please contact support.'
                );

                return;
            }
        }

        if (
            $callsScope !== null
            && !in_array($callsScope, [Checklist::CALLS_SCOPE_FIRST, Checklist::CALLS_SCOPE_ALL], true)
        ) {
            $this->addError(
                'calls_scope',
                sprintf('The value must be either %s or %s', Checklist::CALLS_SCOPE_FIRST, Checklist::CALLS_SCOPE_ALL)
            );
        }

        $callDurationThresholdValidator = new NumberComparison([
            'min' => 1,
            'max' => 500,
        ]);
        if (!$callDurationThresholdValidator->isValid($input['call_duration_threshold'])) {
            $this->addError('call_duration_threshold', 'Value must be between 1 and 500');
        }

        $textLengthValidator = new StringLength([
            'min' => 1,
            'max' => 500,
        ]);

        if (!$textLengthValidator->isValid($checklistName)) {
            $this->addError('checklist_name', 'The value should be between 1 and 1024 characters');
        }

        if (isset($input['description'])) {
            if (!$textLengthValidator->isValid($input['description'])) {
                $this->addError('description', 'The description should not exceed 1024 characters');
            }
        }

        if (isset($input['max_calls_count'])) {
            $callsCountValidator = new \Laminas\Validator\NumberComparison([
                'min' => 1,
            ]);
            if (!$callsCountValidator->isValid($input['max_calls_count'])) {
                $this->addError('max_calls_count', 'Value must be a positive integer');
            }
        }

        if (isset($input['calls_teams'])) {
            if (!is_array($input['calls_teams'])) {
                $this->addError('calls_teams', 'Value must be an array');
            }
        }

        if (isset($input['calls_statuses'])) {
            if (!is_array($input['calls_statuses'])) {
                $this->addError('calls_statuses', 'Value must be an array');
            }
        }
    }
}
