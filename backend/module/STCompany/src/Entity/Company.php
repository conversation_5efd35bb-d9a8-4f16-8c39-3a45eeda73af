<?php

declare(strict_types=1);

namespace STCompany\Entity;

use Carbon\Carbon;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Company
{
    use BaseHydratorTrait;

    public const int DEFAULT_FRONT_ID = 1;
    protected const string KEY_SALT = '+PBffAYK2WXXSE@t';
    protected const int|float START_BALANCE = 100 * 60 * 60;
    protected const int MIN_BALANCE_TO_ANALYSE = 0;

    /**
     *
     * @var int|null
     */
    protected ?int $id = null;

    /**
     *
     * @var string
     */
    protected string $name;

    /**
     *
     * @var float
     */
    protected float $thresholdBar = 0.5;

    /**
     *
     * @var string|null
     */
    protected ?string $avatar = null;

    /**
     *
     * @var string|null
     */
    protected ?string $awsS3BucketRegion = null;

    /**
     *
     * @var string|null
     */
    protected ?string $awsS3BucketName = null;

    /**
     *
     * @var string|null
     */
    protected ?string $awsS3BucketDir = null;

    /**
     *
     * @var string|null
     */
    protected ?string $awsS3ReportBucketName = null;

    /**
     *
     * @var string|null
     */
    protected ?string $awsS3ReportBucketRegion = null;

    protected ?string $awsS3ExportBucketName = null;
    protected ?string $awsS3ExportBucketRegion = null;
    protected ?string $awsS3ExportBucketDir = null;

    /**
     *
     * @var bool|null
     */
    protected ?bool $deleted = false;

    /**
     *
     * @var float
     */
    protected float $paidTranscribingTime = 0;

    /**
     *
     * @var int|null
     */
    protected ?int $minScore = null;

    /**
     *
     * @var int|null
     */
    protected ?int $maxScore = null;

    /**
     *
     * @var int
     */
    protected int $baseScore = 0;

    /**
     *
     * @var int|null
     */
    protected ?int $minCallDurationForAutoAnalyze = null;

    /**
     *
     * @var int|null
     */
    protected ?int $daysToRemoveCalls = null;

    protected int $frontId = self::DEFAULT_FRONT_ID;

    private ?Carbon $lastExportDate = null;

    /**
     *
     * @var array
     */
    protected array $languages = [];

    protected int $llmEventsLimit = 0;

    /**
     *
     * @var CallTemplateCollection
     */
    protected CallTemplateCollection $callTemplates;

    /**
     *
     * @var bool
     */
    protected bool $isManualImportEnabled = true;

    /**
     *
     * @var bool
     */
    protected bool $isChecklistsEnabled = false;

    /**
     * @var bool
     */
    protected bool $isSummarizationEnabled = false;

    /**
     * @var bool
     */
    protected bool $isManageLlmEventsByUsersEnabled = false;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->callTemplates = new CallTemplateCollection();
    }

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     *
     * @return float
     */
    public function getThresholdBar(): float
    {
        return $this->thresholdBar;
    }

    /**
     *
     * @return string|null
     */
    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    /**
     *
     * @return string|null
     */
    public function getAwsS3BucketRegion(): ?string
    {
        return $this->awsS3BucketRegion;
    }


    /**
     *
     * @return string|null
     */
    public function getAwsS3BucketName(): ?string
    {
        return $this->awsS3BucketName;
    }

    /**
     *
     * @param string|null $awsS3ReportBucketName
     * @return Company
     */
    public function setAwsS3ReportBucketName(?string $awsS3ReportBucketName): Company
    {
        $this->awsS3ReportBucketName = $awsS3ReportBucketName;
        return $this;
    }

    /**
     *
     * @return string|null
     */
    public function getAwsS3ReportBucketName(): ?string
    {
        return $this->awsS3ReportBucketName;
    }

    /**
     *
     * @param string|null $awsS3ReportBucketRegion
     * @return Company
     */
    public function setAwsS3ReportBucketRegion(?string $awsS3ReportBucketRegion): Company
    {
        $this->awsS3ReportBucketRegion = $awsS3ReportBucketRegion;
        return $this;
    }

    /**
     *
     * @return string|null
     */
    public function getAwsS3ReportBucketRegion(): ?string
    {
        return $this->awsS3ReportBucketRegion;
    }

    /**
     *
     * @return string|null
     */
    public function getAwsS3BucketDir(): ?string
    {
        return $this->awsS3BucketDir;
    }

    public function setAwsS3ExportBucketName(?string $awsS3ExportBucketName): Company
    {
        $this->awsS3ExportBucketName = $awsS3ExportBucketName;

        return $this;
    }

    public function getAwsS3ExportBucketName(): ?string
    {
        return $this->awsS3ExportBucketName;
    }

    public function setAwsS3ExportBucketRegion(?string $awsS3ExportBucketRegion): Company
    {
        $this->awsS3ExportBucketRegion = $awsS3ExportBucketRegion;

        return $this;
    }

    public function getAwsS3ExportBucketRegion(): ?string
    {
        return $this->awsS3ExportBucketRegion;
    }

    public function setAwsS3ExportBucketDir(?string $awsS3ExportBucketDir): Company
    {
        $this->awsS3ExportBucketDir = $awsS3ExportBucketDir;

        return $this;
    }

    public function getAwsS3ExportBucketDir(): ?string
    {
        return $this->awsS3ExportBucketDir;
    }

    /**
     *
     * @return bool|null
     */
    public function getDeleted(): ?bool
    {
        return $this->deleted;
    }

    /**
     *
     * @return float
     */
    public function getPaidTranscribingTime(): float
    {
        return $this->paidTranscribingTime;
    }

    /**
     *
     * @return CallTemplateCollection
     */
    public function getCallTemplates(): CallTemplateCollection
    {
        return $this->callTemplates;
    }

    /**
     *
     * @return int|null
     */
    public function getMinScore(): ?int
    {
        return $this->minScore;
    }

    /**
     *
     * @return int|null
     */
    public function getMaxScore(): ?int
    {
        return $this->maxScore;
    }

    /**
     *
     * @return int
     */
    public function getBaseScore(): int
    {
        return $this->baseScore;
    }

    /**
     *
     * @return int|null
     */
    public function getMinCallDurationForAutoAnalyze(): ?int
    {
        return $this->minCallDurationForAutoAnalyze;
    }

    /**
     * @return int|null
     */
    public function getDaysToRemoveCalls(): ?int
    {
        return $this->daysToRemoveCalls;
    }

    /**
     * @param int|null $daysToRemoveCalls
     * @return Company
     */
    public function setDaysToRemoveCalls(?int $daysToRemoveCalls): Company
    {
        $this->daysToRemoveCalls = $daysToRemoveCalls;
        return $this;
    }

    /**
     * @return int
     */
    public function getFrontId(): int
    {
        return $this->frontId;
    }

    /**
     * @param int $frontId
     * @return Company
     */
    public function setFrontId(int $frontId): Company
    {
        $this->frontId = $frontId;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function getLanguages(): array
    {
        return $this->languages;
    }

    /**
     *
     * @param int|null $id
     * @return Company
     */
    public function setId(?int $id): Company
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string $name
     * @return Company
     */
    public function setName(string $name): Company
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param float $thresholdBar
     * @return Company
     */
    public function setThresholdBar(float $thresholdBar): Company
    {
        $this->thresholdBar = $thresholdBar;
        return $this;
    }

    /**
     *
     * @param string|null $avatar
     * @return Company
     */
    public function setAvatar(?string $avatar): Company
    {
        $this->avatar = $avatar;
        return $this;
    }

    /**
     *
     * @param string|null $awsS3BucketRegion
     * @return Company
     */
    public function setAwsS3BucketRegion(?string $awsS3BucketRegion): Company
    {
        $this->awsS3BucketRegion = $awsS3BucketRegion;
        return $this;
    }

    /**
     *
     * @param string|null $awsS3BucketName
     * @return Company
     */
    public function setAwsS3BucketName(?string $awsS3BucketName): Company
    {
        $this->awsS3BucketName = $awsS3BucketName;
        return $this;
    }

    /**
     *
     * @param string|null $awsS3BucketDir
     * @return Company
     */
    public function setAwsS3BucketDir(?string $awsS3BucketDir): Company
    {
        $this->awsS3BucketDir = $awsS3BucketDir;
        return $this;
    }

    /**
     *
     * @param bool|null $deleted
     * @return Company
     */
    public function setDeleted(?bool $deleted): Company
    {
        $this->deleted = $deleted;
        return $this;
    }

    /**
     *
     * @param float $paidTranscribingTime
     * @return Company
     */
    public function setPaidTranscribingTime(float $paidTranscribingTime): Company
    {
        $this->paidTranscribingTime = $paidTranscribingTime;
        return $this;
    }

    /**
     * @return bool
     */
    public function isPaidTranscribingTimeEnoughForAnalyze(): bool
    {
        return $this->getPaidTranscribingTime() > self::MIN_BALANCE_TO_ANALYSE;
    }

    /**
     *
     * @param CallTemplateCollection $callTemplates
     * @return Company
     */
    public function setCallTemplates(CallTemplateCollection $callTemplates): Company
    {
        $this->callTemplates = $callTemplates;
        return $this;
    }

    /**
     *
     * @param int|null $minScore
     * @return Company
     */
    public function setMinScore(?int $minScore): Company
    {
        $this->minScore = $minScore;
        return $this;
    }

    /**
     *
     * @param int|null $maxScore
     * @return Company
     */
    public function setMaxScore(?int $maxScore): Company
    {
        $this->maxScore = $maxScore;
        return $this;
    }

    /**
     *
     * @param int $baseScore
     * @return Company
     */
    public function setBaseScore(int $baseScore): Company
    {
        $this->baseScore = $baseScore;
        return $this;
    }

    /**
     *
     * @param int|null $minCallDurationForAutoAnalyze
     * @return Company
     */
    public function setMinCallDurationForAutoAnalyze(?int $minCallDurationForAutoAnalyze): Company
    {
        $this->minCallDurationForAutoAnalyze = $minCallDurationForAutoAnalyze;
        return $this;
    }

    /**
     *
     * @param array $languages
     * @return Company
     */
    public function setLanguages(array $languages): Company
    {
        $this->languages = $languages;
        return $this;
    }

    /**
     *
     * @param bool|null $deleted
     * @return Company|bool|null
     */
    public function isDeleted(bool|null $deleted = null): Company|bool|null
    {
        if (is_null($deleted)) {
            return $this->deleted;
        }
        $this->deleted = $deleted;
        return $this;
    }

    /**
     *
     * @param bool|null $isManualImportEnabled
     * @return Company|bool
     */
    public function isManualImportEnabled(bool|null $isManualImportEnabled = null): Company|bool
    {
        if (is_null($isManualImportEnabled)) {
            return $this->isManualImportEnabled;
        }
        $this->isManualImportEnabled = $isManualImportEnabled;
        return $this;
    }

    /**
     *
     * @param bool|null $isChecklistsEnabled
     * @return Company|bool
     */
    public function isChecklistsEnabled(bool|null $isChecklistsEnabled = null): Company|bool
    {
        if (is_null($isChecklistsEnabled)) {
            return $this->isChecklistsEnabled;
        }
        $this->isChecklistsEnabled = $isChecklistsEnabled;
        return $this;
    }

    public function isSummarizationEnabled(bool|null $isSummarizationEnabled = null): Company|bool
    {
        if (is_null($isSummarizationEnabled)) {
            return $this->isSummarizationEnabled;
        }
        $this->isSummarizationEnabled = $isSummarizationEnabled;
        return $this;
    }

    /**
     *
     * @param string $language
     * @return Company
     */
    public function addLanguage(string $language): Company
    {
        $this->languages[] = $language;
        return $this;
    }

    /**
     *
     * @param float $paidTranscribingTime
     * @return Company
     */
    public function addPaidTranscribingTime(float $paidTranscribingTime): Company
    {
        $this->paidTranscribingTime += $paidTranscribingTime;
        if ($this->paidTranscribingTime < 0) {
            $this->paidTranscribingTime = 0;
        }
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getEncryptionKey(): string
    {
        return md5(static::KEY_SALT . $this->getId());
    }

    /**
     *
     * @return Company
     */
    public function initStartBalance(): Company
    {
        $this->setPaidTranscribingTime(self::START_BALANCE);
        return $this;
    }

    public function getLastExportDate(): ?Carbon
    {
        return $this->lastExportDate;
    }

    public function setLastExportDate(Carbon|string|null $lastExportDate): self
    {
        if (is_string($lastExportDate)) {
            $lastExportDate = new Carbon($lastExportDate);
        }

        $this->lastExportDate = $lastExportDate;

        return $this;
    }

    public function getLlmEventsLimit(): int
    {
        return $this->llmEventsLimit;
    }

    public function setLlmEventsLimit(int $limit): self
    {
        $this->llmEventsLimit = $limit;

        return $this;
    }

    public function isManageLlmEventsByUsersEnabled(bool|null $isManageLlmEventsByUsersEnabled = null): Company|bool
    {
        if (is_null($isManageLlmEventsByUsersEnabled)) {
            return $this->isManageLlmEventsByUsersEnabled;
        }
        $this->isManageLlmEventsByUsersEnabled = $isManageLlmEventsByUsersEnabled;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        $result['call_templates'] = $this->getCallTemplates()->toArray();
        unset($result['deleted']);
        unset($result['is_deleted']);
        unset($result['encryption_key']);
        return $result;
    }
}
