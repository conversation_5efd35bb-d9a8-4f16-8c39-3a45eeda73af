<?php

declare(strict_types=1);

namespace STCompany\Service;

use ReflectionException;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use STCall\Service\AwsTrait;
use STCompany\Data\ClientsTable;
use STCompany\Data\CompaniesCallTemplatesTable;
use STCompany\Data\CompaniesLanguagesTable;
use STCompany\Data\CompaniesRatesTable;
use STCompany\Data\CompanyDetailsTable;
use STCompany\Entity\CallTemplate;
use STCompany\Entity\Company;
use STCompany\Data\CompaniesTable;
use STCompany\Data\TeamsTable;
use STCompany\Data\UsersCompaniesLanguagesTable;
use STCompany\Entity\CompanyDetails;
use STLib\Db\ProvidesTransaction;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STUser\Data\UsersTable;
use UnexpectedValueException;

class CompanyService
{
    use BaseHydratorTrait;
    use AwsTrait;
    use ProvidesTransaction;

    public const string DEFAULT_AWS_REGION = 'eu-west-3';

    /**
     *
     * @var CompaniesTable
     */
    protected CompaniesTable $companiesTable;

    /**
     *
     * @var CompaniesCallTemplatesTable
     */
    protected CompaniesCallTemplatesTable $companiesCallTemplatesTable;

    /**
     *
     * @var CompaniesRatesTable
     */
    protected CompaniesRatesTable $companiesRatesTable;

    /**
     *
     * @var UsersTable
     */
    protected UsersTable $usersTable;

    /**
     *
     * @var CompaniesLanguagesTable
     */
    protected CompaniesLanguagesTable $companiesLanguagesTable;

    /**
     *
     * @var UsersCompaniesLanguagesTable
     */
    protected UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable;

    /**
     *
     * @var TeamsTable
     */
    protected TeamsTable $teamsTable;

    /**
     *
     * @var CallsTable
     */
    protected CallsTable $callsTable;

    /**
     *
     * @var ClientsTable
     */
    protected ClientsTable $clientsTable;

    /**
     * @var CompanyDetailsTable
     */
    protected CompanyDetailsTable $companyDetailsTable;

    /**
     *
     * @param CompaniesTable $companiesTable
     * @param CompaniesCallTemplatesTable $companiesCallTemplatesTable
     * @param CompaniesRatesTable $companiesRatesTable
     * @param UsersTable $usersTable
     * @param CompaniesLanguagesTable $companiesLanguagesTable
     * @param UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable
     * @param TeamsTable $teamsTable
     * @param CallsTable $callsTable
     * @param ClientsTable $clientsTable
     * @param array $awsConfig
     * @throws UnexpectedValueException
     */
    public function __construct(
        CompaniesTable $companiesTable,
        CompaniesCallTemplatesTable $companiesCallTemplatesTable,
        CompaniesRatesTable $companiesRatesTable,
        UsersTable $usersTable,
        CompaniesLanguagesTable $companiesLanguagesTable,
        UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable,
        TeamsTable $teamsTable,
        CallsTable $callsTable,
        ClientsTable $clientsTable,
        CompanyDetailsTable $companyDetailsTable,
        array $awsConfig,
    ) {
        if (!isset($awsConfig['api']) || !isset($awsConfig['env'])) {
            throw new UnexpectedValueException('AWS config must contain "api" and "env" settings');
        }
        $this->companiesTable = $companiesTable;
        $this->companiesCallTemplatesTable = $companiesCallTemplatesTable;
        $this->companiesRatesTable = $companiesRatesTable;
        $this->usersTable = $usersTable;
        $this->companiesLanguagesTable = $companiesLanguagesTable;
        $this->usersCompaniesLanguagesTable = $usersCompaniesLanguagesTable;
        $this->teamsTable = $teamsTable;
        $this->callsTable = $callsTable;
        $this->clientsTable = $clientsTable;
        $this->companyDetailsTable = $companyDetailsTable;
        $this->awsConfig = $awsConfig['api'];
        $this->env = $awsConfig['env'];
    }

    /**
     *
     * @return array
     */
    public function getCompanyIds(): array
    {
        return array_map(function ($companyId) {
            return (int) $companyId;
        }, array_column($this->companiesTable->getCompanyIds()->toArray(), 'company_id'));
    }

    /**
     *
     * @return array
     */
    public function getCompanyIdsWithS3Integration(): array
    {
        return array_map(function ($companyId) {
            return (int) $companyId;
        }, array_column($this->companiesTable->getCompanyIdsWithS3Integration()->toArray(), 'company_id'));
    }

    /**
     *
     * @return array
     */
    public function getCompanyIdsWithCallsRemovingEnabled(): array
    {
        return array_map(function ($companyId) {
            return (int) $companyId;
        }, array_column($this->companiesTable->getCompanyIdsWithCallsRemovingEnabled()->toArray(), 'company_id'));
    }

    /**
     *
     * @return array
     */
    public function getCompanyIdsWithS3ReportIntegration(): array
    {
        return array_map(function ($companyId) {
            return (int) $companyId;
        }, array_column($this->companiesTable->getCompanyIdsWithS3ReportIntegration()->toArray(), 'company_id'));
    }

    /**
     *
     * @param int $userId
     * @return \STCompany\Entity\CompanyCollection
     */
    public function getCompaniesByUserId(int $userId): \STCompany\Entity\CompanyCollection
    {
        $companyCollection = new \STCompany\Entity\CompanyCollection();
        $companiesData = $this->companiesTable->getCompaniesByUserId($userId);
        foreach ($companiesData as $companyData) {
            $companyCollection->add($this->hydrate((array) $companyData, Company::class, withConstructor: true));
        }
        $companyIds = array_column($companyCollection->toArray(), 'id');
        $companyCallTemplates = $this->companiesCallTemplatesTable->getCompanyCallTemplates($companyIds);
        $companyLanguages = $this->companiesLanguagesTable->getCompanyLanguages($companyIds);

        foreach ($companyCallTemplates as $companyCallTemplate) {
            $companyCollection
                    ->offsetGet($companyCallTemplate['company_id'])
                    ->getCallTemplates()
                    ->add($this->hydrate((array) $companyCallTemplate, CallTemplate::class));
        }

        foreach ($companyLanguages as $companyLanguage) {
            $companyCollection
                    ->offsetGet($companyLanguage['company_id'])
                    ->addLanguage($companyLanguage['language']);
        }

        return $companyCollection;
    }

    /**
     * @param int $companyId
     * @param int|null $userId
     * @param bool $withoutRelationships
     * @return Company
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function getCompany(int $companyId, ?int $userId = null, bool $withoutRelationships = false): Company
    {
        $companyData = $this->companiesTable->getCompany($companyId, $userId);
        /** @var Company $company */
        $company = $this->hydrate($companyData->current()->getArrayCopy(), Company::class, withConstructor: true);

        if ($withoutRelationships) {
            return $company;
        }

        $companyCallTemplates = $this->companiesCallTemplatesTable->getCompanyCallTemplates($company->getId());
        $companyLanguages = $this->companiesLanguagesTable->getCompanyLanguages($company->getId());
        $company->setLanguages(array_column($companyLanguages->toArray(), 'language'));
        foreach ($companyCallTemplates as $companyCallTemplate) {
            $company->getCallTemplates()->add($this->hydrate((array) $companyCallTemplate, CallTemplate::class));
        }

        return $company;
    }

    public function getCompanyDetails(int $companyId): CompanyDetails
    {
        return $this->companyDetailsTable->getCompanyDetails($companyId);
    }


    /**
     *
     * @param string $companyToken
     * @return Company
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getCompanyByApiToken(string $companyToken): Company
    {
        $companyData = $this->companiesTable->getCompanyByApiToken($companyToken);
        /** @var Company $company */
        $company = $this->hydrate($companyData->current()->getArrayCopy(), Company::class, withConstructor: true);
        return $company;
    }


    /**
     *
     * @param int $companyId
     * @param array $teamIds
     * @return array
     */
    public function getCompanyStructure(int $companyId, array $teamIds = []): array
    {
        $teamsHierarchy = $this->teamsTable->getTeamsHierarchy($companyId, $teamIds);
        $usersHierarchy = $this->usersTable->getUsersHierarchy($companyId, $teamIds);
        $usersLanguages = $this->usersCompaniesLanguagesTable->getUsersLanguages($companyId, $teamIds);

        $companyHierarchy = new \STCompany\Entity\CompanyHierarchy();
        foreach ($teamsHierarchy as $teamHierarchy) {
            $companyHierarchy->addTeams($teamHierarchy);
        }
        foreach ($usersHierarchy as $userHierarchy) {
            $companyHierarchy->addUsers($userHierarchy);
        }

        $companyHierarchy->injectLanguages($usersLanguages->toArray());

        return $companyHierarchy->toArray();
    }

    /**
     *
     * @param int $companyId
     * @param string $clientSearch
     * @param int $limit
     * @param array $teamIds
     * @return array
     */
    public function getCompanyClients(int $companyId, string $clientSearch, int $limit = 100, array $teamIds = []): array
    {
        return $this->clientsTable->getClientsByIdOrName($companyId, $clientSearch, $limit, $teamIds);
    }

    /**
     *
     * @param Company $company
     * @return string
     */
    public function getCommonAwsBucket(Company $company): string
    {
        $this->setCompany($company);
        return $this->getCommonBucketName();
    }

    /**
     *
     * @param Company $company
     * @return int
     * @throws \Exception
     */
    public function saveCompany(Company $company): int
    {
        $this->beginTransaction();
        try {
            $this->companiesTable->saveCompany($company);
            $this->companiesLanguagesTable->deleteCompanyLanguages($company);
            $this->companiesLanguagesTable->saveCompanyLanguages($company);
            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
        return $company->getId();
    }
    public function saveCompanyDetails(CompanyDetails $companyDetails): bool
    {
        try {
            $this->companyDetailsTable->saveCompanyDetails($companyDetails);
        } catch (\Exception $e) {
            throw $e;
        }
        return true;
    }
    public function updateCompanyDetails(CompanyDetails $companyDetails): bool
    {
        try {
            $this->companyDetailsTable->updateCompanyDetails($companyDetails);
        } catch (\Exception $e) {
            throw $e;
        }
        return true;
    }

    /**
     *
     * @param int $companyId
     * @param float $rate
     * @return void
     * @throws \Exception
     */
    public function saveCompanyRate(int $companyId, float $rate): void
    {
        $this->companiesRatesTable->saveCompanyRate($companyId, $rate);
    }

    /**
     *
     * @param Company $company
     * @return string
     */
    public function createCompanyAwsBucket(Company $company): string
    {
        $this->setCompany($company);
        $this->createBucket();
        return $this->getBucketName();
    }

    /**
     *
     * @param Company $company
     * @return string
     */
    public function createCompanyAwsBucketDir(Company $company): string
    {
        $this->setCompany($company);
        return $this->createCommonBucketDir();
    }

    /**
     *
     * @param Company $company
     * @return int
     */
    public function deleteCompany(Company $company): int
    {
        return $this->companiesTable->deleteCompany($company->getId());
    }

    public function getCompaniesIdsToExport(): array
    {
        return $this->companiesTable->getCompaniesIdsToExport();
    }

    public function updateSettings(int $companyId, array $settings): void
    {
        $this->companiesTable->partialUpdateCompany($companyId, $settings);
    }
}
