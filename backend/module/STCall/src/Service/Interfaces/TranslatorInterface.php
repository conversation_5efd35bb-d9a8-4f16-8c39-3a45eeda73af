<?php

declare(strict_types=1);

namespace STCall\Service\Interfaces;

use GuzzleHttp\Exception\GuzzleException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STTranslation\Service\Drivers\GoogleDriver;

interface TranslatorInterface
{
    /**
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    public function translate(?string $text, string $driverName = GoogleDriver::DRIVER_NAME): string;

    /**
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    public function translateBatch(array $texts, string $driverName, string $languageCode): array;

    public function needBatchTranslate(string $driverName): bool;

    /**
     * @throws GuzzleException
     */
    public function detectLanguage(?string $text): string;

    /**
     * @param string $content
     * @return string
     * @throws GuzzleException
     */
    public function detectContentLanguage(string $content): string;
}
