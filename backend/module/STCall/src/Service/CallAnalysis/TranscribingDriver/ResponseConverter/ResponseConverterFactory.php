<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

class ResponseConverterFactory
{
    /**
     *
     * @param int $type
     * @return ResponseConverterInterface
     * @throws \Exception
     */
    public function create(int $type): ResponseConverterInterface
    {
        return match ($type) {
            ResponseToCollectionConverter::DEEPGRAM => new Deepgram(),
            ResponseToCollectionConverter::WORDCAB => new Wordcab(),
            ResponseToCollectionConverter::AWS => new AWS(),
            ResponseToCollectionConverter::ASSEMBLY => new Assembly(),
            ResponseToCollectionConverter::TRANSCRIPTOR => new Transcriptor(),
            ResponseToCollectionConverter::SALAD => new Salad(),
            ResponseToCollectionConverter::DEEPGRAM_WHISPER => new Deepgram(),
            ResponseToCollectionConverter::SPEECHMATICS => new Speechmatics(),
            default => throw new \Exception('Unknown paragraph response source'),
        };
    }
}
