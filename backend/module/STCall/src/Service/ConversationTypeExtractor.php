<?php

declare(strict_types=1);

namespace STCall\Service;

use STCall\Entity\CallSummarization;

class ConversationTypeExtractor
{
    /**
     * Extracts the conversation type from CallSummarization entity.
     * 
     * The method extracts the actual conversation type from the conversationType property,
     * where the type is enclosed in ** symbols, and excludes the word 'Call' from 
     * conversation type results (e.g., 'Follow-up Call' becomes 'Follow-up', 
     * 'Billing or Account Issue Call' becomes 'Billing or Account Issue').
     * 
     * @param CallSummarization $callSummarization
     * @return string|null The extracted conversation type or null if not found
     */
    public function extractConversationType(CallSummarization $callSummarization): ?string
    {
        $conversationType = $callSummarization->getConversationType();

        if (empty($conversationType)) {
            return null;
        }

        // Extract text between ** symbols
        if (preg_match('/\*\*([^*]+)\*\*/', $conversationType, $matches)) {
            $extractedType = trim($matches[1]);

            // Handle format like "Type of Call: Informational Call"
            if (preg_match('/^Type of Call:\s*(.+)$/i', $extractedType, $colonMatches)) {
                $extractedType = trim($colonMatches[1]);
            }

            // Remove the word "Call" from the end if present
            $extractedType = preg_replace('/\s+Call\s*$/i', '', $extractedType);

            return trim($extractedType);
        }

        return null;
    }
}
