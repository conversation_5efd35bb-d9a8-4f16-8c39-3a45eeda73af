<?php

declare(strict_types=1);

namespace STCall\Entity\CallFragment;

class CallFragment
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const PARAGRAPH_SEPARATOR = "\n";

    /**
     *
     * @var array
     */
    protected \STCall\Entity\ParagraphCollection $paragraphs;

    /**
     *
     * @var \STCall\Entity\Paragraph|null
     */
    protected ?\STCall\Entity\Paragraph $activeParagraph = null;

    /**
     *
     * @var \STCompany\Entity\Event\Color|null
     */
    protected ?\STCompany\Entity\Event\Color $color = null;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->paragraphs = new \STCall\Entity\ParagraphCollection();
    }

    /**
     *
     * @return \STCall\Entity\ParagraphCollection
     */
    public function getParagraphs(): \STCall\Entity\ParagraphCollection
    {
        return $this->paragraphs;
    }

    /**
     *
     * @return \STCompany\Entity\Event\Color
     */
    public function getColor(): \STCompany\Entity\Event\Color
    {
        if (is_null($this->color)) {
            $this->defineAttributes();
        }
        return $this->color;
    }

    /**
     *
     * @return \STCall\Entity\Paragraph
     */
    public function getActiveParagraph(): \STCall\Entity\Paragraph
    {
        if (is_null($this->activeParagraph)) {
            $this->defineAttributes();
        }
        return $this->activeParagraph;
    }

    /**
     *
     * @param \STCall\Entity\ParagraphCollection $paragraphs
     * @return CallFragment
     */
    public function setParagraphs(\STCall\Entity\ParagraphCollection $paragraphs): CallFragment
    {
        $this->paragraphs = $paragraphs;
        return $this;
    }

    /**
     *
     * @param int $paragraphId
     * @return bool
     */
    public function hasParagraph(int $paragraphId): bool
    {
        return in_array($paragraphId, $this->getParagraphs()->keys());
    }

    /**
     *
     * @param \STCall\Entity\Paragraph|null $activeParagraph
     * @return CallFragment
     */
    public function setActiveParagraph(?\STCall\Entity\Paragraph $activeParagraph): CallFragment
    {
        $this->activeParagraph = $activeParagraph;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\Event\Color|null $color
     * @return CallFragment
     */
    public function setColor(?\STCompany\Entity\Event\Color $color): CallFragment
    {
        $this->color = $color;
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getGluedText(): string
    {
        return implode(static::PARAGRAPH_SEPARATOR, array_column($this->getParagraphs()->toArray(), 'text'));
    }

    /**
     *
     * @param string $needle
     * @return int
     */
    public function getParagraphNumberByText(string $needle): int
    {
        $maxTextSimilarity = 0;
        $maxTextSimilarityParagraphNumber = 0;
        $percent = 0;
        foreach ($this->getParagraphs() as $paragraph) {
            if (str_contains($paragraph->getText(), $needle)) {
                return $paragraph->getParagraphNumber();
            }

            /**
             * If needle separated by few paragraphs
             */
            similar_text($needle, $paragraph->getText(), $percent);
            if ($percent > $maxTextSimilarity) {
                $maxTextSimilarity = $percent;
                $maxTextSimilarityParagraphNumber = $paragraph->getParagraphNumber();
            }
        }
        return $maxTextSimilarityParagraphNumber;
    }

    /**
     *
     * @return float
     */
    public function getStartTime(): float
    {
        return $this->getParagraphs()->first()->getStartTime();
    }

    /**
     *
     * @return float
     */
    public function getEndTime(): float
    {
        return $this->getParagraphs()->last()->getStartTime();
    }

    /**
     *
     * @return CallFragment
     */
    protected function defineAttributes(): CallFragment
    {
        foreach ($this->getParagraphs() as $paragraph) {
            if (is_null($this->activeParagraph)) {
                $this->activeParagraph = $paragraph;
            }
            foreach ($paragraph->getEventHappenings() as $eventHappening) {
                if (!$eventHappening->isDeleted() && $eventHappening->getEvent()->getColor()->getPriority() < $this->getColor()->getPriority()) {
                    $this->color = $eventHappening->getEvent()->getColor();
                    $this->activeParagraph = $paragraph;
                }
            }
        }
        return $this;
    }

    /**
     *
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = []): array
    {
        $result = $this->extract($this);
        $result['paragraphs'] = $this->getParagraphs()->toArray();
        $result['color'] = $this->getColor()->toArray();
        $result['active_paragraph'] = $this->getActiveParagraph()->toArray();

        if (empty($attributes)) {
            return $result;
        }

        return array_filter($result, function ($attribute) use ($attributes) {
            return in_array($attribute, $attributes);
        }, ARRAY_FILTER_USE_KEY);
    }
}
