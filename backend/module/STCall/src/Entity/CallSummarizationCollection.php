<?php

declare(strict_types=1);

namespace STCall\Entity;

use STLib\Expand\Collection;

class CallSummarizationCollection extends Collection
{
    public function add(mixed $callSummarization, string|int|null $key = null): Collection
    {
        if (!($callSummarization instanceof CallSummarization)) {
            throw new \RuntimeException('Call summary must be an instace of "\STCall\Entity\CallSummarization"');
        }
        parent::add($callSummarization, $key ?? $this->getOffset<PERSON>ey($callSummarization->getCallId(), $callSummarization->getCompanyId()));

        return $this;
    }

    public function getCallSummarization(string $callId, int $companyId): ?CallSummarization
    {
        return $this->offsetGet($this->getOffsetKey($callId, $companyId));
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];

        foreach ($this as $callSummarization) {
            $result[] = $callSummarization->toArray();
        }

        return $result;
    }

    private function getOffsetKey(string $callId, int $companyId): string
    {
        return $callId . '-' . $companyId;
    }
}
