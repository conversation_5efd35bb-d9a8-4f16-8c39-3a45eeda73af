<?php

declare(strict_types=1);

namespace STCall\Entity;

class Notification
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected int $companyId;

    /**
     *
     * @var string
     */
    protected string $callId;

    /**
     *
     * @var string
     */
    protected string $commentId;

    /**
     *
     * @var int
     */
    protected int $userId;

    /**
     *
     * @var bool
     */
    protected bool $isUnread = true;

    /**
     *
     * @var \Carbon\Carbon
     */
    protected ?\Carbon\Carbon $created = null;

    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @return string
     */
    public function getCallId(): string
    {
        return $this->callId;
    }

    /**
     *
     * @return string
     */
    public function getCommentId(): string
    {
        return $this->commentId;
    }

    /**
     *
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     *
     * @return bool
     */
    public function getIsUnread(): bool
    {
        return $this->isUnread;
    }

    /**
     *
     * @return \Carbon\Carbon
     */
    public function getCreated(): \Carbon\Carbon
    {
        $this->initCreated();
        return $this->created;
    }

    /**
     *
     * @param string $callId
     * @return Notification
     */
    public function setCallId(string $callId): Notification
    {
        $this->callId = $callId;
        return $this;
    }

    /**
     *
     * @param string $commentId
     * @return Notification
     */
    public function setCommentId(string $commentId): Notification
    {
        $this->commentId = $commentId;
        return $this;
    }

    /**
     *
     * @param int $companyId
     * @return Notification
     */
    public function setCompanyId(int $companyId): Notification
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param int $userId
     * @return Notification
     */
    public function setUserId(int $userId): Notification
    {
        $this->userId = $userId;
        return $this;
    }

    /**
     *
     * @param bool $isUnread
     * @return Notification
     */
    public function setIsUnread(bool $isUnread): Notification
    {
        $this->isUnread = $isUnread;
        return $this;
    }

    /**
     *
     * @param string|\Carbon\Carbon $created
     * @return Notification
     */
    public function setCreated(string|\Carbon\Carbon $created): Notification
    {
        $this->created = is_string($created) ? \Carbon\Carbon::parse($created) : $created;
        return $this;
    }

    /**
     *
     * @param bool|null $isUnread
     * @return Comment|bool
     */
    public function isUnread(?bool $isUnread = null): Notification|bool
    {
        if (is_null($isUnread)) {
            return $this->isUnread;
        }
        $this->isUnread = $isUnread;
        return $this;
    }

    /**
     *
     * @param bool $force
     * @return Notification
     */
    public function initCreated(bool $force = false): Notification
    {
        if ($force || is_null($this->created)) {
            $this->created = \Carbon\Carbon::now();
        }
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        return $result;
    }
}
