<?php

declare(strict_types=1);

namespace STCall\Entity\Import\Result;

class Team
{
    /**
     *
     * @var null|string|int
     */
    protected null|string|int $id = null;

    /**
     *
     * @var string
     */
    protected ?string $name = null;

    /**
     *
     * @return null|string|int
     */
    public function getId(): null|string|int
    {
        return $this->id;
    }

    /**
     *
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     *
     * @param null|string|int $id
     * @return Team
     */
    public function setId(null|string|int $id): Team
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string|null $name
     * @return Team
     */
    public function setName(?string $name): Team
    {
        $this->name = $name;
        return $this;
    }
}
