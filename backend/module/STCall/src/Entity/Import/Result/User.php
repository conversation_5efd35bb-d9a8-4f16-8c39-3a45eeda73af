<?php

declare(strict_types=1);

namespace STCall\Entity\Import\Result;

class User
{
    /**
     *
     * @var null|string|int
     */
    protected null|string|int $id = null;

    /**
     *
     * @var string|null
     */
    protected ?string $name = null;

    /**
     *
     * @return null|string|int
     */
    public function getId(): null|string|int
    {
        return $this->id;
    }

    /**
     *
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     *
     * @param null|string|int $id
     * @return User
     */
    public function setId(null|string|int $id): User
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string|null $name
     * @return User
     */
    public function setName(?string $name): User
    {
        $this->name = $name;
        return $this;
    }
}
