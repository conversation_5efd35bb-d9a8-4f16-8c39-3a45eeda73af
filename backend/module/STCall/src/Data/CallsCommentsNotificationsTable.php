<?php

declare(strict_types=1);

namespace STCall\Data;

class CallsCommentsNotificationsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param string|null $callId
     * @return array
     */
    public function getNotifications(int $companyId, int $userId, string|array $callId = []): array
    {
        $callIds = is_array($callId) ? $callId : [$callId];
        $sql = '
            SELECT
                *
            FROM
            (
                ' . $this->getFinalTableSql('calls_comments_notifications', [
                    'comment_id',
                    'user_id'
                ], 'created') . '
            )
            WHERE
                user_id = \'' . $userId . '\'
                AND company_id = \'' . $companyId . '\'
        ';
        if (count($callIds)) {
            $sql .= '
                AND call_id IN (\'' . implode('\',\'', $callIds) . '\')
            ';
        }
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param array $teamIds
     * @return int
     */
    public function getUnreadCallNotificationsCount(int $companyId, int $userId, array $teamIds = []): int
    {
        $sql = '
            SELECT
                COUNT(*)
            FROM
                (
                    SELECT
                        call_id
                    FROM
                        (
                            SELECT
                                call_id,
                                agent_id
                            FROM
                            (
                                ' .
                                $this->getFinalTableSqlUsingGroupBy(
                                    'calls',
                                    [
                                        'company_id',
                                        'call_id'
                                    ],
                                    'created',
                                    [
                                        'agent_id',
                                        'is_deleted',
                                    ],
                                    [
                                        'company_id' => $companyId,
                                        'is_deleted' => 0,
                                        [
                                            'type' => 'expression',
                                            'value' => '
                                                call_id IN
                                                (
                                                    SELECT
                                                        call_id
                                                    FROM
                                                        calls_comments_notifications ccn FINAL
                                                    WHERE 
                                                        company_id = ' . $companyId . '
                                                        AND user_id = ' . $userId . '
                                                        AND is_unread = 1
                                                )
                                            ',
                                        ],
                                    ]
                                ) . '
                            )
                        ) c
        ';
        if (count($teamIds) > 0) {
            $sql .= '
                    LEFT JOIN
                        (SELECT * FROM dictionary(users_teams)) ut
                        ON c.agent_id = ut.user_id
                    WHERE
                        ut.team_id IN (' . implode(',', $teamIds) . ')
            ';
        }
        $sql .= '
                )
        ';
        return (int) $this->getClient()->selectValue($sql);
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param array $unreadComments
     * @return array
     */
    public function getCallIdsWithNotifications(int $companyId, int $userId, array $unreadComments = []): array
    {
        $sql = '
            SELECT
                call_id
            FROM
            (
                SELECT
                    call_id,
                    MAX(is_unread) is_unread
                FROM
                (
                    ' . $this->getFinalTableSqlUsingGroupBy('calls_comments_notifications', [
                            'comment_id',
                            'call_id',
                        ], 'created', [
                            'is_unread',
                        ], [
                            'company_id' => $companyId,
                            'user_id' => $userId,
                        ]) . '
                )
                GROUP BY
                    call_id
            )
        ';
        if (!empty($unreadComments)) {
            $sql .= '
            WHERE
                is_unread IN (' . implode(',', $unreadComments) . ')
            ';
        }
        return $this->getClient()->selectColumn($sql, 'call_id');
    }

    /**
     *
     * @param \STCall\Entity\NotificationCollection $notifications
     * @return string
     */
    public function saveNotifications(\STCall\Entity\NotificationCollection $notifications): int
    {
        if ($notifications->count() === 0) {
            return 0;
        }
        foreach ($notifications->chunk(100) as $notificationsChunk) {
            $data = [];
            foreach ($notificationsChunk as $notification) {
                $record = $notification->toArray();
                $record['is_unread'] = (int) $record['is_unread'];
                $data[] = $record;
            }
            $columns = array_keys(current($data));
            $this->getClient()->insert('calls_comments_notifications', $data, $columns);
        }
        return $notifications->count();
    }
}
