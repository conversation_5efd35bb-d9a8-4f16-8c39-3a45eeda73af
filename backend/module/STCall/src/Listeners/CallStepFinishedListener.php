<?php

declare(strict_types=1);

namespace STCall\Listeners;

use <PERSON><PERSON>\EventManager\EventInterface;
use PhpAmqpLib\Message\AMQPMessage;
use ST<PERSON>all\Daemon\Webhooks\WebhooksDaemon;
use STCall\Service\Webhooks\WebhookServiceFactory;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use STRabbit\Service\RabbitService;
use STRoboTruck\Service\DataCollection\DataCollector;

class CallStepFinishedListener
{
    private const string ERROR_MESSAGE = 'Fail to extract call data.';

    public function __construct(
        private readonly WebhookServiceFactory $webhookServiceFactory,
        private readonly WebhookSettingsSelector $webhookSettingsSelector,
        private readonly RabbitService $rabbit,
        private readonly DataCollector $dataCollector
    ) {
    }

    public function listen(EventInterface $event): void
    {
        $companyId = $event->getParam('company_id');
        $callId = $event->getParam('call_id');
        $data = $event->getParam('data');
        $queueName = $event->getParam('queue_name');

        if (is_null($companyId) || is_null($callId) || is_null($data) || is_null($queueName)) {
            $this->dataCollector->collect(
                DataCollector::EVENT_ADD_STEP_RESULTS_TO_WEBHOOKS_QUEUE_FAIL,
                self::ERROR_MESSAGE,
                ['event' => json_encode($event)]
            );

            return;
        }

        $webhookService = $this->webhookServiceFactory->create($queueName);
        if (!$this->webhookSettingsSelector->isWebhooksEnabled($webhookService->getType(), $companyId)) {
            return;
        }

        $messageBody = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'source' => $queueName,
            'data' => $data,
        ];

        $channel = $this->rabbit->getChannel();

        $message = new AMQPMessage(json_encode($messageBody));
        $channel->basic_publish($message, '', WebhooksDaemon::WEBHOOKS_QUEUE_NAME);
        $channel->close();
    }
}
