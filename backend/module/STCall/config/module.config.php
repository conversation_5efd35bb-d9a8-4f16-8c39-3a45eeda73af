<?php

declare(strict_types=1);

namespace STCall;

use ST<PERSON>lickhouse\Entity\TableFactory;
use STLib\Mvc\DependencyInjection\DefaultFactory;
use STTranslation\Service\Whisper\Sdk;
use STTranslation\Service\Whisper\SdkFactory;

return [
    'controller_plugins' => [
        'invokables' => [
            'call' => Controller\Plugin\Call::class,
            'translator' => Controller\Plugin\Translator::class,
        ]
    ],
    'service_manager' => [
        'factories' => [
            Data\CallsTable::class => TableFactory::class,
            Data\CallsParagraphsTable::class => TableFactory::class,
            Data\PrecalculatedCallsTable::class => TableFactory::class,
            Data\PrecalculatedCallsEventsTable::class => TableFactory::class,
            Data\EventsRepository::class => TableFactory::class,
            Data\EventHappeningsChangesTable::class => TableFactory::class,
            Data\CallsCommentsTable::class => TableFactory::class,
            Data\CallsCommentsNotificationsTable::class => TableFactory::class,
            Data\CallsReviewsTable::class => TableFactory::class,
            Data\CallsChecklistsPointsTable::class => TableFactory::class,
            Data\TranscribeDriversLanguagesTable::class => \STLib\Mvc\Data\TableFactory::class,
            Data\TranslationDriversLanguagesTable::class => \STLib\Mvc\Data\TableFactory::class,
            Sdk::class => SdkFactory::class,
            Service\Import\UploadService::class => Service\Import\UploadServiceFactory::class,
            Service\CallAnalysisService::class => DefaultFactory::class,
            Service\CallReviewService::class => DefaultFactory::class,
            Service\EventHappeningService::class => DefaultFactory::class,
            Service\CallChecklistService::class => DefaultFactory::class,
            Service\EventService::class => DefaultFactory::class,
            Service\LanguageDriverService::class => DefaultFactory::class,
            Service\CallAnalysis\TranscribingDistributionStep::class => DefaultFactory::class,
            Service\CallAnalysis\TranscribingStep::class => Service\CallAnalysis\TranscribingStepFactory::class,
            Service\CallAnalysis\SpeakersRolesDetectionStep::class => DefaultFactory::class,
            Service\CallAnalysis\ChecklistStep::class => DefaultFactory::class,
            Service\CallAnalysis\TranscribingWhisperStep::class => Service\CallAnalysis\TranscribingWhisperStepFactory::class,
            Service\CallAnalysis\TranscribingJobCreationStep::class => Service\CallAnalysis\TranscribingJobCreationStepFactory::class,
            Service\CallAnalysis\TranscribingJobCollectionStep::class => Service\CallAnalysis\TranscribingJobCollectionStepFactory::class,
            Service\CallAnalysis\TranslationStep::class => DefaultFactory::class,
            Service\CallAnalysis\AlgoEventsStep::class => DefaultFactory::class,
            Service\CallAnalysis\Mp3FileConvertationStep::class => Service\CallAnalysis\Mp3FileConvertationStepFactory::class,
            Service\CallAnalysis\LanguageDetectionStep::class => Service\CallAnalysis\LanguageDetectionStepFactory::class,
            Service\ConversationTypeExtractor::class => DefaultFactory::class,
            Service\Precalculation\CallPrecalculationService::class => DefaultFactory::class,
            Service\Precalculation\CallPrecalculationManagerService::class => DefaultFactory::class,
            Validator\CallValidator::class => Validator\CallValidatorFactory::class,
        ]
    ]
];
