<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use STApi\Entity\Exception\ValidationApiException;
use STReport\Entity\ReportTemplate;
use STReport\Service\ReportTemplateService;
use STReport\Validator\ReportTemplateValidator;

class ReportTemplateController extends BaseController
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     * @return array
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function templatesAction(): array
    {
        return [
            'templates' => $this->report()->template()->getReportTemplates($this->auth()->getUser()->getId())->toArray(),
        ];
    }

    /**
     * @return array
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function templateAction(): array
    {
        $userId = $this->auth()->getUser()->getId();

        return [
            'template' => $this->report()->template()->getReportTemplate((int) $this->getApiParam('template_id'), $userId)->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ValidationApiException
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function saveAction(): array
    {
        $templateId = (int) $this->getApiParam('template_id');
        $filters = $this->getApiParam('filters');

        /** @var ReportTemplate $template */
        $template = $this->hydrate([
            'report' => $this->getApiParam('report') ?? ReportTemplate::DEFAULT_REPORT,
            'name' => $this->getApiParam('name'),
            'icon' => $this->getApiParam('icon'),
            'user_id' => $this->auth()->getUser()->getId(),
            'filters' => $filters,
        ], ReportTemplate::class);

        /** @var ReportTemplateValidator $validator */
        $validator = $this->getServiceManager()->get(ReportTemplateValidator::class);
        $validator->setInstance($template);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        if ($templateId > 0) {
            $userId = $this->auth()->getUser()->getId();
            $this->report()->template()->getReportTemplate($templateId, $userId);
            $template->setId($templateId);
        }

        $templateId = $this->report()->template()->saveReportTemplate($template);

        return [
            'template' => $this->report()->template()->getReportTemplate($templateId, $this->auth()->getUser()->getId())->toArray(),
        ];
    }

    /**
     * @return array
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function deleteAction(): array
    {
        $templateId = (int) $this->getApiParam('template_id');
        $userId = $this->auth()->getUser()->getId();
        $this->report()->template()->getReportTemplate($templateId, $userId);

        $this->report()->template()->deleteTemplate($templateId);

        return [
            'deleted' => true,
        ];
    }
}
