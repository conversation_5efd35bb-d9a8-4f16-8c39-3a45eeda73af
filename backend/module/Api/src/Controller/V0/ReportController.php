<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Exception;
use Laminas\Http\Response;
use STClickhouse\Entity\Pagination\CandidatePagination;
use STLib\Expand\CsvConverter;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class ReportController extends BaseController
{
    use BaseHydratorTrait;
    use CsvConverter;

    /**
     *
     * @return array
     * @throws Exception
     */
    public function callsAction(): array
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $filters = $this->getApiParam('filter') ?? [];
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;
        $hasCommentsOnly = (bool) $this->getApiParam('has_comments_only');
        $hasUnreadComments = $this->hasApiParam('has_unread_comments') ? (int) $this->getApiParam('has_unread_comments') : null;

        $filters['team_id'] = $this->getTeamIdsFilterValues($user, $filters['team_id'] ?? []);

        if ($hasCommentsOnly) {
            $filters['has_comments_only'] = [
                'user_id' => $user->getId(),
                'has_unread_comments' => $hasUnreadComments,
            ];
        }

        $pagination = new CandidatePagination();

        $pagination
            ->setFilter($filters)
            ->setResultCallback(function ($record) {
                if (isset($record['fragments'])) {
                    $record['fragments'] = array_map(function ($fragment) {
                        $fragment['fragment_number'] = (int) $fragment['fragment_number'];
                        $fragment['start_time'] = (float) $fragment['start_time'];
                        $fragment['end_time'] = (float) $fragment['end_time'];
                        $fragment['active_paragraph_number'] = (int) $fragment['active_paragraph_number'];
                        return $fragment;
                    }, $record['fragments']);
                }
                if (isset($record['teams'])) {
                    $record['teams'] = array_map(function ($team) {
                        $team['team_id'] = (int) $team['team_id'];
                        return $team;
                    }, $record['teams']);
                }
                if (isset($record['reviewers'])) {
                    $record['reviewers'] = array_map(function ($reviewer) {
                        $reviewer['id'] = (int) $reviewer['id'];
                        return $reviewer;
                    }, $record['reviewers']);
                }
                if (isset($record['events'])) {
                    $record['events'] = array_map(function ($event) {
                        $event['event_id'] = (int) $event['event_id'];
                        $event['category_id'] = (int) $event['category_id'];
                        $event['color_id'] = (int) $event['color_id'];
                        $event['count'] = (int) $event['count'];
                        return $event;
                    }, $record['events']);
                }
                return $record;
            })
            ->getParams()
            ->setPageNumber((int) $this->getApiParam('page_number'))
            ->setItemsOnPage((int) $this->getApiParam('items_on_page'))
            ->setSort($this->getApiParam('sort') ?? [])
            ->addAvailableColumns([
                'company_id',
                'call_id',
                'role_id',
                'call_time',
                'call_type',
                'agent_id',
                'client_id',
                'call_origin',
                'call_status',
                'conversation_type',
                'call_duration',
                'is_analyzed',
                'is_reviewed',
                'is_partly_reviewed',
                'uploaded_time',
                'reviewed_time',
                'reviewer_user_id',
                'event_ids',
                'event_category_ids',
                'score',
                'risk_rank',
            ]);

        $this->report()->call()->getCalls(
            $pagination,
            $this->company->getId(),
            $user->getRole()->getId(),
            $startDate,
            $endDate,
        );

        return $pagination->toArray();
    }

    /**
     *
     * @return array
     * @throws Exception
     */
    public function clientsReportAction(): array
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $filters = $this->getApiParam('filter') ?? [];
        $columns = $this->getApiParam('columns') ?? [];
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;

        $filters['team_ids'] = $this->getTeamIdsFilterValues($user, $filters['team_ids'] ?? []);

        $pagination = new CandidatePagination();
        $pagination
            ->setFilter($filters)
            ->setResultCallback(function ($record) {
                if (isset($record['events'])) {
                    $record['events'] = array_map(function ($event) {
                        $event['event_id'] = (int) $event['event_id'];
                        $event['category_id'] = (int) $event['category_id'];
                        $event['color_id'] = (int) $event['color_id'];
                        $event['count'] = (int) $event['count'];
                        return $event;
                    }, $record['events']);
                }
                if (isset($record['detailed_events'])) {
                    $record['detailed_events'] = array_map(function ($detailedEvent) {
                        $detailedEvent['start_time'] = (int) $detailedEvent['start_time'];
                        $detailedEvent['paragraph_number'] = (int) $detailedEvent['paragraph_number'];
                        $detailedEvent['event_id'] = (int) $detailedEvent['event_id'];
                        $detailedEvent['color_id'] = (int) $detailedEvent['color_id'];
                        $detailedEvent['category_id'] = (int) $detailedEvent['category_id'];
                        return $detailedEvent;
                    }, $record['detailed_events']);
                }
                return $record;
            })
            ->getParams()
            ->setColumns($columns)
            ->setPageNumber((int) $this->getApiParam('page_number'))
            ->setItemsOnPage((int) $this->getApiParam('items_on_page'))
            ->setSort($this->getApiParam('sort') ?? [])
            ->addAvailableColumns([
                'company_id',
                'role_id',
                'client_id',
                'client_name',
                'agent_name',
                'team_name',
                'total_calls_duration',
                'effective_calls_duration',
                'not_effective_calls_duration',
                'effective_calls_count',
                'not_effective_calls_count',
                'reviewed_calls_count',
                'reviewed_chats_count',
                'score',
                'risk_rank',
                'last_call_time',
                'country',
                'status',
                'source',
                'acquisition_date',
                'is_converted',
                'converted_date',
                'last_transaction_date',
                'campaign_id',
                'value',
                'detailed_events',
                'events',
            ]);

        $this->report()->client()->getClientsReport(
            $pagination,
            $this->company->getId(),
            $user->getRole()->getId(),
            $startDate,
            $endDate,
        );

        return $pagination->toArray();
    }

    /**
     *
     * @return Response
     * @throws Exception
     */
    public function clientsReportCsvAction(): Response
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $filters = $this->getApiParam('filter') ?? [];
        $columns = $this->getApiParam('columns') ?? [];
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;

        $filters['team_ids'] = $this->getTeamIdsFilterValues($user, $filters['team_ids'] ?? []);

        $result = $this->report()->client()->getClientsCsvReport(
            $this->company->getId(),
            $user->getRole()->getId(),
            $startDate,
            $endDate,
            $filters,
            $columns,
        );

        return $this->output(
            $this->convertArrayToCsv(iterator_to_array($result)),
            Response::STATUS_CODE_200,
            static::CSV_FORMAT,
            [
                'fileName' => 'report_qc_' . date('Y-m-d'),
            ],
        );
    }

    /**
     *
     * @return array
     * @throws Exception
     */
    public function clientReportAction(): array
    {
        $clientId = $this->getApiParam('client_id');
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $clientReport = $this->report()->client()->getClientReport($this->company->getId(), $user->getRole()->getId(), $clientId);

        if (isset($clientReport['detailed_events'])) {
            array_walk($clientReport['detailed_events'], function (&$detailedEvent) {
                $detailedEvent['start_time'] = (int) $detailedEvent['start_time'];
                $detailedEvent['paragraph_number'] = (int) $detailedEvent['paragraph_number'];
                $detailedEvent['event_id'] = (int) $detailedEvent['event_id'];
                $detailedEvent['color_id'] = (int) $detailedEvent['color_id'];
                $detailedEvent['category_id'] = (int) $detailedEvent['category_id'];
            });
        }
        if (isset($clientReport['events'])) {
            array_walk($clientReport['events'], function (&$event) {
                $event['event_id'] = (int) $event['event_id'];
                $event['category_id'] = (int) $event['category_id'];
                $event['color_id'] = (int) $event['color_id'];
                $event['count'] = (int) $event['count'];
            });
        }
        if (isset($clientReport['last_agent']['user_id'])) {
            $clientReport['last_agent']['user_id'] = (int) $clientReport['last_agent']['user_id'];
        }

        return [
            'client' => $clientReport,
        ];
    }

    /**
     *
     * @return array
     * @throws Exception
     */
    public function agentsReportCalendarAction(): array
    {
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date')) : \Carbon\Carbon::now()->subMonth();
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date')) : \Carbon\Carbon::now();
        $dateDimension = $this->getApiParam('date_dimension') ?? \STCall\Data\CallsTable::DAY_CALENDAR_DATE_DIMENSIONS;
        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());
        $agentId = $this->hasApiParam('agent_id') ? (int) $this->getApiParam('agent_id') : null;

        return [
            'calendar' => $this->report()->agent()->getCalendar(
                $this->company->getId(),
                $startDate,
                $endDate,
                $dateDimension,
                $teamIds,
                $agentId,
            ),
        ];
    }

    public function agentsReportAction(): array
    {
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;

        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $filters = $this->getApiParam('filter') ?? [];

        $filters['team_ids'] = $this->getTeamIdsFilterValues($user, $filters['team_ids'] ?? []);

        $agentReport = array_map(function ($agentReport) {
            if (isset($agentReport['events'])) {
                array_walk($agentReport['events'], function (&$event) {
                    $event['event_id'] = (int) $event['event_id'];
                    $event['category_id'] = (int) $event['category_id'];
                    $event['color_id'] = (int) $event['color_id'];
                    $event['count'] = (int) $event['count'];
                });
            }
            if (isset($agentReport['detailed_events'])) {
                array_walk($agentReport['detailed_events'], function (&$detailedEvent) {
                    $detailedEvent['start_time'] = (int) $detailedEvent['start_time'];
                    $detailedEvent['paragraph_number'] = (int) $detailedEvent['paragraph_number'];
                    $detailedEvent['event_id'] = (int) $detailedEvent['event_id'];
                    $detailedEvent['color_id'] = (int) $detailedEvent['color_id'];
                    $detailedEvent['category_id'] = (int) $detailedEvent['category_id'];
                });
            }
            if (isset($agentReport['teams'])) {
                array_walk($agentReport['teams'], function (&$team) {
                    $team['team_id'] = (int) $team['team_id'];
                });
            }
            return $agentReport;
        }, $this->report()->agent()->getAgentsReport($this->company, $user->getRole()->getId(), $startDate, $endDate, $filters));

        return [
            'report' => $agentReport,
        ];
    }

    public function agentReportAction(): array
    {
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;
        $agentId = (int) $this->getApiParam('agent_id');

        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());

        $agentReport = $this->report()->agent()->getAgentReport($this->company, $user->getRole()->getId(), $agentId, $startDate, $endDate);

        if (isset($agentReport['detailed_events'])) {
            array_walk($agentReport['detailed_events'], function (&$detailedEvent) {
                $detailedEvent['start_time'] = (int) $detailedEvent['start_time'];
                $detailedEvent['paragraph_number'] = (int) $detailedEvent['paragraph_number'];
                $detailedEvent['event_id'] = (int) $detailedEvent['event_id'];
                $detailedEvent['color_id'] = (int) $detailedEvent['color_id'];
                $detailedEvent['category_id'] = (int) $detailedEvent['category_id'];
            });
        }
        if (isset($agentReport['events'])) {
            array_walk($agentReport['events'], function (&$event) {
                $event['event_id'] = (int) $event['event_id'];
                $event['category_id'] = (int) $event['category_id'];
                $event['color_id'] = (int) $event['color_id'];
                $event['count'] = (int) $event['count'];
            });
        }
        if (isset($agentReport['teams'])) {
            array_walk($agentReport['teams'], function (&$team) {
                $team['team_id'] = (int) $team['team_id'];
            });
        }

        return [
            'agent' => $agentReport,
        ];
    }

    /**
     *
     * @return array
     * @throws Exception
     */
    public function callsReportAction(): array
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $filters = $this->getApiParam('filter') ?? [];
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;

        $filters['team_id'] = $this->getTeamIdsFilterValues($user, $filters['team_id'] ?? []);

        $pagination = new CandidatePagination();

        $pagination
            ->setFilter($filters)
            ->setResultCallback(function ($record) {
                if (isset($record['fragments'])) {
                    $record['fragments'] = array_map(function ($fragment) {
                        $fragment['fragment_number'] = (int) $fragment['fragment_number'];
                        $fragment['start_time'] = (float) $fragment['start_time'];
                        $fragment['end_time'] = (float) $fragment['end_time'];
                        $fragment['active_paragraph_number'] = (int) $fragment['active_paragraph_number'];
                        return $fragment;
                    }, $record['fragments']);
                }
                if (isset($record['teams'])) {
                    $record['teams'] = array_map(function ($team) {
                        $team['team_id'] = (int) $team['team_id'];
                        return $team;
                    }, $record['teams']);
                }
                if (isset($record['reviewers'])) {
                    $record['reviewers'] = array_map(function ($reviewer) {
                        $reviewer['id'] = (int) $reviewer['id'];
                        return $reviewer;
                    }, $record['reviewers']);
                }
                if (isset($record['events'])) {
                    $record['events'] = array_map(function ($event) {
                        $event['event_id'] = (int) $event['event_id'];
                        $event['paragraph_number'] = (int) $event['paragraph_number'];
                        $event['paragraph_start_time'] = (int) $event['paragraph_start_time'];
                        $event['category_id'] = (int) $event['category_id'];
                        $event['event_color_id'] = (int) $event['event_color_id'];
                        $event['event_color_priority'] = (int) $event['event_color_priority'];
                        return $event;
                    }, $record['events']);
                }
                return $record;
            })
            ->getParams()
            ->setPageNumber((int) $this->getApiParam('page_number'))
            ->setItemsOnPage((int) $this->getApiParam('items_on_page'))
            ->setSort($this->getApiParam('sort') ?? [])
            ->addAvailableColumns([
                'company_id',
                'call_id',
                'call_time',
                'call_type',
                'call_language',
                'call_status',
                'call_duration',
                'paragraphs_count',
                'uploaded_time',
                'is_transcribed',
                'is_translated',
                'is_analyzed',
                'is_sent_to_transcribing',
                'original_file_name',
                'uploaded_user_id',
                'uploaded_user_name',
                'agent_id',
                'agent_name',
                'client_id',
                'client_name',
                'client_country',
                'client_status',
                'client_source',
                'client_acquisition_date',
                'client_is_converted',
                'client_last_transaction_date',
                'client_converted_date',
                'client_campaign_id',
                'client_value',
                'call_origin',
                'is_partly_reviewed',
                'reviewed_time',
                'reviewer_user_id',
                'event_ids',
                'event_category_ids',
                'score',
                'risk_rank',
                'reviewers',
                'fragments',
                'conversation_type',
                'teams',
                'events',
            ]);

        $this->report()->call()->getCallsReport(
            $pagination,
            $this->company->getId(),
            $user->getRole()->getId(),
            $startDate,
            $endDate,
        );

        return $pagination->toArray();
    }

    /**
     *
     * @return Response
     * @throws Exception
     */
    public function callsReportCsvAction(): Response
    {
        $baseCallUrl = $this->getApiParam('base_call_url');
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $filters = $this->getApiParam('filter') ?? [];
        $columns = $this->getApiParam('columns') ?? [];
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;

        if (count($columns) > 0) {
            $columns = array_intersect($columns, [
                'call_id',
                'call_time',
                'call_type',
                'call_language',
                'call_status',
                'call_duration',
                'paragraphs_count',
                'uploaded_time',
                'original_file_name',
                'uploaded_user_id',
                'uploaded_user_name',
                'agent_name',
                'client_id',
                'client_name',
                'client_country',
                'client_status',
                'client_source',
                'client_acquisition_date',
                'client_is_converted',
                'client_converted_date',
                'client_last_transaction_date',
                'client_campaign_id',
                'client_value',
                'call_origin',
                'is_reviewed',
                'is_partly_reviewed',
                'reviewed_time',
                'reviewer_user_id',
                'score',
                'risk_rank',
                'conversation_type',
                'reviewers',
                'teams',
                'events',
                'en_events',
                'url',
            ]);
        }

        $filters['team_id'] = $this->getTeamIdsFilterValues($user, $filters['team_id'] ?? []);

        $result = $this->report()->call()->getCallsCsvReport(
            $this->company->getId(),
            $user->getRole()->getId(),
            $startDate,
            $endDate,
            $filters,
            $columns,
            $baseCallUrl,
        );

        return $this->output(
            $result,
            Response::STATUS_CODE_200,
            static::CSV_FORMAT,
            [
                'fileName' => 'report_qc_' . date('Y-m-d'),
            ],
        );
    }

    /**
     *
     * @return array
     */
    public function callsReportValuesRangesAction(): array
    {
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;

        return [
            'valuesRanges' => $this->report()->call()->getFilterValuesRanges($this->company->getId(), $startDate, $endDate),
        ];
    }

    /**
     *
     * @param \STUser\Entity\User $user
     * @param array $filteredTeamIds
     * @return array
     */
    private function getTeamIdsFilterValues(\STUser\Entity\User $user, array $filteredTeamIds): array
    {
        $availableTeamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $user->getId());
        if (empty($filteredTeamIds)) {
            return $availableTeamIds;
        }
        if (empty($availableTeamIds)) {
            return $filteredTeamIds;
        }
        $intersect = array_intersect($filteredTeamIds, $availableTeamIds);
        if (count($intersect) > 0) {
            return $intersect;
        }
        return [\STCompany\Service\UserService::FAKE_NOT_EXISTED_TEAM_ID];
    }
}
