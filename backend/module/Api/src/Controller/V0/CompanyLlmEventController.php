<?php

namespace Api\Controller\V0;

use Laminas\Http\Header\ContentType;
use Lam<PERSON>\Http\Headers;
use <PERSON>inas\Stdlib\ResponseInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ValidationApiException;
use STCompany\Validator\ConnectLlmEventValidator;
use STCompany\Validator\DisconnectLlmEventValidator;
use STCompany\Validator\LlmEvent\CreateLlmEventValidator;
use Throwable;

class CompanyLlmEventController extends BaseController
{
    /**
     * @return array|ResponseInterface
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     * @throws Throwable
     * @throws ValidationApiException
     */
    public function createAction(): array|ResponseInterface
    {
        if (!$this->company->isManageLlmEventsByUsersEnabled()) {
            $error = 'LLM event manage is currently disabled for your company. Contact support for assistance.';
            $this->getResponse()->setStatusCode(403);
            $this->getResponse()
                ->setContent(
                    json_encode([
                        'error' => [
                            'message' => $error
                        ]
                    ])
                );
            $header = new ContentType('application/json');
            $this->getResponse()->getHeaders()->addHeader($header);

            return $this->getResponse();
        }

        $name = $this->getApiParam('name');
        $description = $this->getApiParam('description');

        /** @var CreateLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(CreateLlmEventValidator::class);
        $validator->setInstance([
            'company' => $this->company,
            'name' => $name,
            'description' => $description
        ]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $llmEvent = $this->company()->llmEventCreator()->create($name, $description, $this->company->getId());

        return [
            'event' => $llmEvent->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getEventsAction(): array
    {
        return [
            'events' => $this->company()->llmEventSelector()->getLlmEvents($this->company->getId())->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     */
    public function getEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        return [
            'event' => $this->company()->llmEventSelector()->getLlmEvent($id, $this->company->getId())->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     */
    public function connectEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var ConnectLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(ConnectLlmEventValidator::class);
        $validator->setInstance(['llm_event_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $event = $this->company()->llmEventConnector()->connect($id, $this->company->getId());

        return [
            'event' => $event->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function disconnectEventAction(): array|ResponseInterface
    {
        if (!$this->company->isManageLlmEventsByUsersEnabled()) {
            $error = 'LLM event manage is currently disabled for your company. Contact support for assistance.';
            $this->getResponse()->setStatusCode(403);
            $this->getResponse()
                ->setContent(
                    json_encode([
                        'error' => [
                            'message' => $error
                        ]
                    ])
                );
            $header = new ContentType('application/json');
            $this->getResponse()->getHeaders()->addHeader($header);

            return $this->getResponse();
        }

        $id = (int) $this->getApiParam('id');

        /** @var DisconnectLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(DisconnectLlmEventValidator::class);
        $validator->setInstance(['llm_event_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->llmEventConnector()->disconnect($id, $this->company->getId());

        return [
            'is_deleted' => true,
            'message' => 'Successfully disconnected event from company.',
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function disconnectEventByGlobalAdminAction(): array|ResponseInterface
    {
        $id = (int) $this->getApiParam('id');

        /** @var DisconnectLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(DisconnectLlmEventValidator::class);
        $validator->setInstance(['llm_event_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->llmEventConnector()->disconnect($id, $this->company->getId());

        return [
            'is_deleted' => true,
            'message' => 'Successfully disconnected event from company.',
        ];
    }
}
