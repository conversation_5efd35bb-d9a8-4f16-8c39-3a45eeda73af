<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use STApi\Entity\Exception\BadRequestApiException;
use STCompany\Entity\Role;
use STCompany\Entity\Team;
use STCompany\Entity\User;
use STCompany\Service\UserService;
use STCompany\Validator\CompanyUserValidator;
use STMail\Service\MailService;
use STUser\Service\AuthService;
use STUser\Service\UserAvatarService;

class CompanyUsersController extends BaseController
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    public const INVITE_CODE_LIFETIME = 60 * 60 * 24 * 365;

    /**
     * @return array
     */
    public function getCompanyUsersAction(): array
    {
        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());
        $users = $this->company()->user()->getUsers($this->company->getId(), $teamIds)->toArray();
        return [
            'users' => $users,
        ];
    }

    /**
     * return array
     */
    public function getCompanyUserAction(): array
    {
        $companyId = (int) $this->company->getId();
        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());
        $userId = (int) $this->getApiParam('user_id');
        $user = $this->company()->user()->getUser($companyId, $userId, $teamIds);
        return [
            'user' => $user->toArray(),
        ];
    }

    /**
     *
     *
     * @return array
     */
    public function getUserNotificationsCountAction(): array
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $user->getId());
        $unreadCallNotificationsCount = $this->call()->getUnreadCallNotificationsCount($this->company->getId(), $user->getId(), $teamIds);
        return [
            'unread_call_notifications_count' => $unreadCallNotificationsCount,
        ];
    }

    /**
     *
     * return array
     */
    public function getAgentsAction(): array
    {
        $companyId = (int) $this->company->getId();
        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());
        $filters = $this->getApiParam('filter');
        $filters = is_array($filters) ? array_filter($filters) : $filters;

        if (isset($filters['team_id'])) {
            $teamIds = count($teamIds) > 0 ? array_intersect($teamIds, $filters['team_id']) : $filters['team_id'];
        }

        return [
            'users' => $this->company()->user()->getUsersByRoleType($companyId, [
                Role::AGENT_ROLE_TYPE,
            ], $teamIds)->toArray([
                'id',
                'email',
                'name',
                'is_auto_analyze_calls',
            ]),
        ];
    }

    /**
     *
     * return array
     */
    public function getManagersAction(): array
    {
        $companyId = (int) $this->company->getId();
        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());
        $filters = $this->getApiParam('filter');
        $filters = is_array($filters) ? array_filter($filters) : $filters;

        if (isset($filters['team_id'])) {
            $teamIds = count($teamIds) > 0 ? array_intersect($teamIds, $filters['team_id']) : $filters['team_id'];
        }

        return [
            'users' => $this->company()->user()->getUsersByRoleType($companyId, [
                Role::COMPANY_ADMIN_ROLE_TYPE,
                Role::COMPANY_SUPERVISOR_ROLE_TYPE,
                Role::MANAGER_ROLE_TYPE,
            ], $teamIds)->toArray([
                'id',
                'email',
                'name',
                'is_auto_analyze_calls',
            ]),
        ];
    }

    /**
     *
     * @return array
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     * @throws \STApi\Entity\Exception\ValidationApiException
     */
    public function saveCompanyUserAction(): array
    {
        $apiParams = $this->getApiParams()->toArray();
        /** @var User $user */
        $user = $this->hydrate($apiParams, User::class, withConstructor: true);

        if ($user->getEmail()) {
            $user->setEmail(trim($user->getEmail()));
        }

        $validationChecks = [
            'name',
            'avatar',
            'teams-exists',
        ];

        /** @var UserService $userService */
        $userService = $this->company()->user();

        /**
         * 3 possible ways:
         * - change existed company user
         * - add existed Robonote user to company
         * - add new user to Robonote
         */
        $sendInviteEmail = true;
        if (!is_null($user->getId())) {
            // change existed company user
            $currentUserTeamIds = $userService->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());
            $existedCompanyUser = $userService->getUser($this->company->getId(), $user->getId(), $currentUserTeamIds);
            $user->setId($existedCompanyUser->getId())
                ->setTwoFactorSecret($existedCompanyUser->getTwoFactorSecret())
                ->setRegistrationDate($existedCompanyUser->getRegistrationDate())
                ->isFirstLogin(!empty($user->getPassword()) ? false : $existedCompanyUser->isFirstLogin());
            unset($existedCompanyUser);
            $sendInviteEmail = false;
        } else {
            try {
                // add existed Robonote user to company
                $existedUser = $this->user()->getUserByEmail($user->getEmail());
                $user->setId($existedUser->getId())
                    ->setTwoFactorSecret($existedUser->getTwoFactorSecret())
                    ->setRegistrationDate($existedUser->getRegistrationDate())
                    ->isFirstLogin($existedUser->isFirstLogin());
            } catch (\STApi\Entity\Exception\NotFoundApiException $e) {
                // add new user to Robonote
                $user->initRegistrationDate();
                $user->isFirstLogin(true);
                $validationChecks[] = 'email-not-exists';
            }
        }

        $roleId = (int) $apiParams['role_id'];

        if ($roleId > 0) {
            $role = $this->company()->role()->getRole($this->company->getId(), $roleId);
            $user->setRole($role);
        }

        $teamIds = $apiParams['team_ids'] ?? [];

        if (count($teamIds) > 1 && $user->getRole()->isAgent()) {
            throw new BadRequestApiException('Agent can be assigned to only one team');
        }

        foreach ($teamIds as $teamId) {
            $user->getTeams()->add((new Team())->setId($teamId)->setCompanyId($this->company->getId()));
        }

        if (!empty($user->getPassword())) {
            $validationChecks[] = 'password';
        }

        // clear users two factor secret if param two_factor_secret passed
        if ($this->hasApiParam('two_factor_secret')) {
            $user->setTwoFactorSecret(null);
        }

        /** @var CompanyUserValidator $validator */
        $validator = $this->getServiceManager()->get(CompanyUserValidator::class);
        $validator->setInstance($user)->setCompanyId($this->company->getId());
        $validator->validate($validationChecks);

        if (!empty($user->getPassword())) {
            $config = $this->getServiceManager()->get('config');
            $user->hashPassword($config['auth']['salt']);
        }

        if ($validator->hasError()) {
            throw new \STApi\Entity\Exception\ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $userService->saveUser($user, $this->company->getId());

        if ($sendInviteEmail) {
            if ($user->isFirstLogin() && !empty($apiParams['redirect_url'])) {
                $this->sendNewUserInviteEmail($user, $this->auth()->getUser()->getName(), $this->company->getName(), $apiParams['redirect_url']);
            }

            if (!$user->isFirstLogin()) {
                $this->sendExistedUserInviteEmail($user, $this->auth()->getUser()->getName(), $this->company->getName());
            }
        }

        /** @var UserAvatarService $avatarService */
        $avatarService = $this->user()->avatar();
        $avatarService->deleteAvatar($user);

        return [
            'user' => $userService->getUser($this->company->getId(), $user->getId())->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function changeCompanyUserActivityAction(): array
    {
        $companyId = $this->company->getId();
        $userId = (int)$this->getApiParam('user_id');

        // check access to user and copy optional data to new user object
        $teamIds = $this->company()->user()->getUserTeamIds($companyId, $this->auth()->getUser()->getId());
        $this->company()->user()->getUser($companyId, $userId, $teamIds);

        $isActive = $this->getApiParam('is_active');
        $this->company()->user()->changeCompanyUserActivity($companyId, $userId, $isActive);
        return [
            'is_active' => $this->company()->user()->getCompanyUserActivity($companyId, $userId),
        ];
    }

    /**
     *
     * @return array
     */
    public function deleteCompanyUserAction(): array
    {
        $userId = (int) $this->getApiParam('user_id');

        // check access to user and copy optional data to new user object
        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());
        $this->company()->user()->getUser($this->company->getId(), $userId, $teamIds);

        $this->company()->user()->deleteUser($userId, $this->company->getId());
        return [
            'deleted' => true,
        ];
    }

    /**
     *
     * @param User $user
     * @param string $senderName
     * @param string $companyName
     * @param string $redirectUrl
     * @return void
     */
    private function sendNewUserInviteEmail(User $user, string $senderName, string $companyName, string $redirectUrl): void
    {
        if (str_contains($redirectUrl, '{code}')) {
            /** @var AuthService $authService */
            $authService = $this->auth();
            $code = $authService->getChangePasswordCode($user, static::INVITE_CODE_LIFETIME);
            $redirectUrl = $this->front()->makeAbsolutUrl(str_replace('{code}', $code, $redirectUrl));
        }

        /** @var MailService $mailService */
        $mailService = $this->mail();
        $mailService->addToQueue($this->front()->getActiveFront()->getId(), $user, [
            'template_id' => 'invite-new-user',
            'substitutions' => [
                'dynamic_html' => [
                    'account_link' => $redirectUrl,
                    'invitation_link' => $redirectUrl,
                ],
                'inviter' => $senderName,
                'company_name' => $companyName,
                'user_email' => $user->getEmail(),
            ],
        ]);
    }

    /**
     *
     * @param User $user
     * @param string $senderName
     * @param string $companyName
     * @return void
     */
    private function sendExistedUserInviteEmail(User $user, string $senderName, string $companyName): void
    {
        $activeFront = $this->front()->getActiveFront();

        /** @var MailService $mailService */
        $mailService = $this->mail();
        $mailService->addToQueue($activeFront->getId(), $user, [
            'template_id' => 'invite-to-company',
            'substitutions' => [
                'inviter' => $senderName,
                'subject' => sprintf('%s: invite to %s', $activeFront->getSendingEmailSender(), $companyName),
                'company_name' => $companyName,
                'user_email' => $user->getEmail(),
            ],
        ]);
    }
}
