<?php

declare(strict_types=1);

namespace Api\Controller\RoboMetrics;

class AlgoStatisticsController extends BaseController
{
    /**
     *
     * @return array
     */
    public function getAlgoEventsCountPerParagraphAction(): array
    {
        return [
            'result' => $this->roboMetrics()->algoStatistics()->getAlgoEventsCountPerParagraphByDays(
                \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay(),
                \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay(),
                $this->getApiParam('score') ? (float) $this->getApiParam('score') : null,
            ),
        ];
    }

    /**
     *
     * @return array
     */
    public function getAlgoEventsCountPerCallAction(): array
    {
        return [
            'result' => $this->roboMetrics()->algoStatistics()->getAlgoEventsCountPerCallByDays(
                \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay(),
                \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay(),
                $this->getApiParam('score') ? (float) $this->getApiParam('score') : null,
            ),
        ];
    }

    /**
     *
     * @return array
     */
    public function getAverageScorePerEventAction(): array
    {
        return [
            'result' => $this->roboMetrics()->algoStatistics()->getAverageScorePerEventByDays(
                \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay(),
                \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay(),
                in_array($this->getApiParam('algo_event_name'), ['none', null], true) ? null : (string) $this->getApiParam('algo_event_name'),
            ),
        ];
    }

    /**
     *
     * @return array
     */
    public function getParagraphsCountPerCallAction(): array
    {
        return [
            'result' => $this->roboMetrics()->algoStatistics()->getParagraphsCountPerCallByDays(
                \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay(),
                \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay(),
                in_array($this->getApiParam('company_id'), ['none', null], true) ? null : (int) $this->getApiParam('company_id'),
            ),
        ];
    }

    /**
     *
     * @return array
     */
    public function getParagraphsLengthAction(): array
    {
        return [
            'result' => $this->roboMetrics()->algoStatistics()->getParagraphsLengthByDays(
                \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay(),
                \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay(),
                in_array($this->getApiParam('company_id'), ['none', null], true) ? null : (int) $this->getApiParam('company_id'),
            ),
        ];
    }

    /**
     *
     * @return array
     */
    public function getAlgoEventsStatisticsAction(): array
    {
        return [
            'result' => $this->roboMetrics()->algoStatistics()->getAlgoEventsStatistics(
                \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay(),
                \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay(),
                in_array($this->getApiParam('algo_event_name'), ['none', null], true) ? null : (string) $this->getApiParam('algo_event_name'),
            ),
        ];
    }
}
